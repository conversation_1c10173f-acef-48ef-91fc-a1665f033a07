import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const SecretGarden: React.FC = () => {
  const { user } = useAuth();
  const [showPremiumModal, setShowPremiumModal] = useState(!user?.isPremium);
  
  // Mock premium features
  const premiumFeatures = [
    {
      icon: 'bi-eye-fill',
      title: 'See Who Likes You',
      description: 'View all users who have sent you apples without having to match first.'
    },
    {
      icon: 'bi-apple',
      title: 'Unlimited Apples',
      description: 'Send as many apples as you want without daily limits.'
    },
    {
      icon: 'bi-arrow-counterclockwise',
      title: 'Undo Last Swipe',
      description: 'Change your mind? Undo your last swipe and reconsider.'
    },
    {
      icon: 'bi-star-fill',
      title: 'Priority Profile',
      description: 'Your profile gets shown to more users, increasing your chances of matching.'
    },
    {
      icon: 'bi-filter',
      title: 'Advanced Filters',
      description: 'Filter potential matches by more specific criteria like interests, education, and more.'
    },
    {
      icon: 'bi-calendar-event',
      title: 'Exclusive Events',
      description: 'Access to premium-only virtual events with curated attendees.'
    }
  ];
  
  if (showPremiumModal) {
    return (
      <div className="min-h-screen bg-eden-green-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl p-6 max-w-md w-full leaf-shadow"
        >
          <div className="text-center mb-6">
            <div className="w-20 h-20 rounded-full bg-eden-gold-100 flex items-center justify-center mx-auto mb-4">
              <i className="bi bi-key-fill text-eden-gold-500 text-3xl"></i>
            </div>
            
            <h2 className="text-2xl font-bold text-eden-green-800 mb-2 font-eden">
              Unlock the Secret Garden
            </h2>
            
            <p className="text-eden-green-700">
              Upgrade to premium to access exclusive features and enhance your dating experience.
            </p>
          </div>
          
          <div className="space-y-4 mb-6">
            {premiumFeatures.slice(0, 3).map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="w-8 h-8 rounded-full bg-eden-gold-100 flex items-center justify-center mr-3 mt-1">
                  <i className={`${feature.icon} text-eden-gold-500`}></i>
                </div>
                <div>
                  <h4 className="font-medium text-eden-green-800">{feature.title}</h4>
                  <p className="text-sm text-eden-green-600">{feature.description}</p>
                </div>
              </div>
            ))}
            
            <div className="text-center text-eden-green-600">
              <button className="flex items-center justify-center w-full">
                <span>See all premium features</span>
                <i className="bi bi-chevron-down ml-1"></i>
              </button>
            </div>
          </div>
          
          <div className="space-y-3">
            <Button
              variant="gold"
              fullWidth
              icon="bi-unlock"
            >
              Upgrade to Premium
            </Button>
            
            <button 
              onClick={() => setShowPremiumModal(false)}
              className="w-full text-center py-2 text-eden-green-600"
            >
              Maybe Later
            </button>
          </div>
        </motion.div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">Secret Garden</h1>
        <p className="text-eden-green-600">Exclusive premium features</p>
      </header>
      
      {/* Premium features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {premiumFeatures.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl p-5 leaf-shadow"
          >
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-eden-gold-100 flex items-center justify-center mr-3 mt-1">
                <i className={`${feature.icon} text-eden-gold-500`}></i>
              </div>
              <div>
                <h3 className="font-medium text-eden-green-800 mb-1">{feature.title}</h3>
                <p className="text-sm text-eden-green-600">{feature.description}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Premium matches */}
      <div className="bg-white rounded-2xl p-6 leaf-shadow mb-6">
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Premium Matches</h2>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          {[1, 2, 3, 4].map((_, index) => (
            <div key={index} className="relative">
              <div className="aspect-square rounded-xl overflow-hidden">
                <img 
                  src={`https://images.unsplash.com/photo-${index % 2 === 0 ? '1534528741775-53994a69daeb' : '1539571696357-5a69c17a67c6'}?auto=format&fit=crop&w=300&q=80`} 
                  alt="Premium match" 
                  className="w-full h-full object-cover filter blur-sm"
                  crossOrigin="anonymous"
                />
              </div>
              
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-10 h-10 rounded-full bg-eden-gold-500 flex items-center justify-center">
                  <i className="bi bi-lock-fill text-white"></i>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <p className="text-eden-green-700 text-sm mb-4">
          These users have shown interest in your profile. Upgrade to see who they are and match instantly!
        </p>
        
        <Button
          variant="gold"
          fullWidth
          icon="bi-unlock"
        >
          Unlock Premium Matches
        </Button>
      </div>
      
      {/* Subscription options */}
      <div className="bg-white rounded-2xl p-6 leaf-shadow">
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Choose Your Plan</h2>
        
        <div className="space-y-4 mb-6">
          <div className="border-2 border-eden-gold-500 bg-eden-gold-50 rounded-xl p-4 relative">
            <div className="absolute -top-3 right-4 bg-eden-gold-500 text-eden-green-900 px-3 py-1 rounded-full text-xs font-medium">
              Best Value
            </div>
            
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-eden-green-800">6 Months</h3>
              <div className="text-right">
                <div className="text-eden-green-800 font-bold">$9.99/mo</div>
                <div className="text-eden-green-600 text-xs line-through">$14.99/mo</div>
              </div>
            </div>
            
            <p className="text-eden-green-700 text-sm">
              Save 33% with our 6-month plan. Total: $59.94
            </p>
          </div>
          
          <div className="border-2 border-eden-green-200 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-eden-green-800">3 Months</h3>
              <div className="text-right">
                <div className="text-eden-green-800 font-bold">$12.99/mo</div>
                <div className="text-eden-green-600 text-xs line-through">$14.99/mo</div>
              </div>
            </div>
            
            <p className="text-eden-green-700 text-sm">
              Save 13% with our 3-month plan. Total: $38.97
            </p>
          </div>
          
          <div className="border-2 border-eden-green-200 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-eden-green-800">1 Month</h3>
              <div className="text-eden-green-800 font-bold">$14.99/mo</div>
            </div>
            
            <p className="text-eden-green-700 text-sm">
              Try premium with our monthly plan. Total: $14.99
            </p>
          </div>
        </div>
        
        <Button
          variant="gold"
          fullWidth
          icon="bi-unlock"
        >
          Start Premium
        </Button>
      </div>
    </div>
  );
};

export default SecretGarden;
