{"version": 3, "sources": ["../../react-swipeable/src/types.ts", "../../react-swipeable/src/index.ts"], "sourcesContent": ["import * as React from \"react\";\n\nexport const LEFT = \"Left\";\nexport const RIGHT = \"Right\";\nexport const UP = \"Up\";\nexport const DOWN = \"Down\";\nexport type HandledEvents = React.MouseEvent | TouchEvent | MouseEvent;\nexport type Vector2 = [number, number];\nexport type SwipeDirections =\n  | typeof LEFT\n  | typeof RIGHT\n  | typeof UP\n  | typeof DOWN;\nexport interface SwipeEventData {\n  /**\n   * Absolute displacement of swipe in x. Math.abs(deltaX);\n   */\n  absX: number;\n  /**\n   * Absolute displacement of swipe in y. Math.abs(deltaY);\n   */\n  absY: number;\n  /**\n   * Displacement of swipe in x. (current.x - initial.x)\n   */\n  deltaX: number;\n  /**\n   * Displacement of swipe in y. (current.y - initial.y)\n   */\n  deltaY: number;\n  /**\n   * Direction of swipe - Left | Right | Up | Down\n   */\n  dir: SwipeDirections;\n  /**\n   * Source event.\n   */\n  event: HandledEvents;\n  /**\n   * True for the first event of a tracked swipe.\n   */\n  first: boolean;\n  /**\n   * Location where swipe started - [x, y].\n   */\n  initial: Vector2;\n  /**\n   * \"Absolute velocity\" (speed) - √(absX^2 + absY^2) / time\n   */\n  velocity: number;\n  /**\n   * Velocity per axis - [ deltaX/time, deltaY/time ]\n   */\n  vxvy: Vector2;\n}\n\nexport type SwipeCallback = (eventData: SwipeEventData) => void;\nexport type TapCallback = ({ event }: { event: HandledEvents }) => void;\n\nexport type SwipeableDirectionCallbacks = {\n  /**\n   * Called after a DOWN swipe\n   */\n  onSwipedDown: SwipeCallback;\n  /**\n   * Called after a LEFT swipe\n   */\n  onSwipedLeft: SwipeCallback;\n  /**\n   * Called after a RIGHT swipe\n   */\n  onSwipedRight: SwipeCallback;\n  /**\n   * Called after a UP swipe\n   */\n  onSwipedUp: SwipeCallback;\n};\n\nexport type SwipeableCallbacks = SwipeableDirectionCallbacks & {\n  /**\n   * Called at start of a tracked swipe.\n   */\n  onSwipeStart: SwipeCallback;\n  /**\n   * Called after any swipe.\n   */\n  onSwiped: SwipeCallback;\n  /**\n   * Called for each move event during a tracked swipe.\n   */\n  onSwiping: SwipeCallback;\n  /**\n   * Called after a tap. A touch under the min distance, `delta`.\n   */\n  onTap: TapCallback;\n  /**\n   * Called for `touchstart` and `mousedown`.\n   */\n  onTouchStartOrOnMouseDown: TapCallback;\n  /**\n   * Called for `touchend` and `mouseup`.\n   */\n  onTouchEndOrOnMouseUp: TapCallback;\n};\n\n// Configuration Options\nexport type ConfigurationOptionDelta =\n  | number\n  | { [key in Lowercase<SwipeDirections>]?: number };\n\nexport interface ConfigurationOptions {\n  /**\n   * Min distance(px) before a swipe starts. **Default**: `10`\n   */\n  delta: ConfigurationOptionDelta;\n  /**\n   * Prevents scroll during swipe in most cases. **Default**: `false`\n   */\n  preventScrollOnSwipe: boolean;\n  /**\n   * Set a rotation angle. **Default**: `0`\n   */\n  rotationAngle: number;\n  /**\n   * Track mouse input. **Default**: `false`\n   */\n  trackMouse: boolean;\n  /**\n   * Track touch input. **Default**: `true`\n   */\n  trackTouch: boolean;\n  /**\n   * Allowable duration of a swipe (ms). **Default**: `Infinity`\n   */\n  swipeDuration: number;\n  /**\n   * Options for touch event listeners\n   */\n  touchEventOptions: { passive: boolean };\n}\n\nexport type SwipeableProps = Partial<SwipeableCallbacks & ConfigurationOptions>;\n\nexport type SwipeablePropsWithDefaultOptions = Partial<SwipeableCallbacks> &\n  ConfigurationOptions;\n\nexport interface SwipeableHandlers {\n  ref(element: HTMLElement | null): void;\n  onMouseDown?(event: React.MouseEvent): void;\n}\n\nexport type SwipeableState = {\n  cleanUpTouch?: () => void;\n  el?: HTMLElement;\n  eventData?: SwipeEventData;\n  first: boolean;\n  initial: Vector2;\n  start: number;\n  swiping: boolean;\n  xy: Vector2;\n};\n\nexport type StateSetter = (\n  state: SwipeableState,\n  props: SwipeablePropsWithDefaultOptions\n) => SwipeableState;\nexport type Setter = (stateSetter: StateSetter) => void;\nexport type AttachTouch = (\n  el: HTMLElement,\n  props: SwipeablePropsWithDefaultOptions\n) => () => void;\n", "/* global document */\nimport * as React from \"react\";\nimport {\n  AttachTouch,\n  SwipeDirections,\n  DOWN,\n  SwipeEventData,\n  HandledEvents,\n  LEFT,\n  RIGHT,\n  Setter,\n  ConfigurationOptions,\n  SwipeableDirectionCallbacks,\n  SwipeableHandlers,\n  SwipeableProps,\n  SwipeablePropsWithDefaultOptions,\n  SwipeableState,\n  SwipeCallback,\n  TapCallback,\n  UP,\n  Vector2,\n} from \"./types\";\n\nexport {\n  LEFT,\n  RIGHT,\n  UP,\n  DOWN,\n  SwipeDirections,\n  SwipeEventData,\n  SwipeableDirectionCallbacks,\n  SwipeCallback,\n  TapCallback,\n  SwipeableHandlers,\n  SwipeableProps,\n  Vector2,\n};\n\nconst defaultProps: ConfigurationOptions = {\n  delta: 10,\n  preventScrollOnSwipe: false,\n  rotationAngle: 0,\n  trackMouse: false,\n  trackTouch: true,\n  swipeDuration: Infinity,\n  touchEventOptions: { passive: true },\n};\nconst initialState: SwipeableState = {\n  first: true,\n  initial: [0, 0],\n  start: 0,\n  swiping: false,\n  xy: [0, 0],\n};\nconst mouseMove = \"mousemove\";\nconst mouseUp = \"mouseup\";\nconst touchEnd = \"touchend\";\nconst touchMove = \"touchmove\";\nconst touchStart = \"touchstart\";\n\nfunction getDirection(\n  absX: number,\n  absY: number,\n  deltaX: number,\n  deltaY: number\n): SwipeDirections {\n  if (absX > absY) {\n    if (deltaX > 0) {\n      return RIGHT;\n    }\n    return LEFT;\n  } else if (deltaY > 0) {\n    return DOWN;\n  }\n  return UP;\n}\n\nfunction rotateXYByAngle(pos: Vector2, angle: number): Vector2 {\n  if (angle === 0) return pos;\n  const angleInRadians = (Math.PI / 180) * angle;\n  const x =\n    pos[0] * Math.cos(angleInRadians) + pos[1] * Math.sin(angleInRadians);\n  const y =\n    pos[1] * Math.cos(angleInRadians) - pos[0] * Math.sin(angleInRadians);\n  return [x, y];\n}\n\nfunction getHandlers(\n  set: Setter,\n  handlerProps: { trackMouse: boolean | undefined }\n): [\n  {\n    ref: (element: HTMLElement | null) => void;\n    onMouseDown?: (event: React.MouseEvent) => void;\n  },\n  AttachTouch\n] {\n  const onStart = (event: HandledEvents) => {\n    const isTouch = \"touches\" in event;\n    // if more than a single touch don't track, for now...\n    if (isTouch && event.touches.length > 1) return;\n\n    set((state, props) => {\n      // setup mouse listeners on document to track swipe since swipe can leave container\n      if (props.trackMouse && !isTouch) {\n        document.addEventListener(mouseMove, onMove);\n        document.addEventListener(mouseUp, onUp);\n      }\n      const { clientX, clientY } = isTouch ? event.touches[0] : event;\n      const xy = rotateXYByAngle([clientX, clientY], props.rotationAngle);\n\n      props.onTouchStartOrOnMouseDown &&\n        props.onTouchStartOrOnMouseDown({ event });\n\n      return {\n        ...state,\n        ...initialState,\n        initial: xy.slice() as Vector2,\n        xy,\n        start: event.timeStamp || 0,\n      };\n    });\n  };\n\n  const onMove = (event: HandledEvents) => {\n    set((state, props) => {\n      const isTouch = \"touches\" in event;\n      // Discount a swipe if additional touches are present after\n      // a swipe has started.\n      if (isTouch && event.touches.length > 1) {\n        return state;\n      }\n\n      // if swipe has exceeded duration stop tracking\n      if (event.timeStamp - state.start > props.swipeDuration) {\n        return state.swiping ? { ...state, swiping: false } : state;\n      }\n\n      const { clientX, clientY } = isTouch ? event.touches[0] : event;\n      const [x, y] = rotateXYByAngle([clientX, clientY], props.rotationAngle);\n      const deltaX = x - state.xy[0];\n      const deltaY = y - state.xy[1];\n      const absX = Math.abs(deltaX);\n      const absY = Math.abs(deltaY);\n      const time = (event.timeStamp || 0) - state.start;\n      const velocity = Math.sqrt(absX * absX + absY * absY) / (time || 1);\n      const vxvy: Vector2 = [deltaX / (time || 1), deltaY / (time || 1)];\n\n      const dir = getDirection(absX, absY, deltaX, deltaY);\n\n      // if swipe is under delta and we have not started to track a swipe: skip update\n      const delta =\n        typeof props.delta === \"number\"\n          ? props.delta\n          : props.delta[dir.toLowerCase() as Lowercase<SwipeDirections>] ||\n            defaultProps.delta;\n      if (absX < delta && absY < delta && !state.swiping) return state;\n\n      const eventData = {\n        absX,\n        absY,\n        deltaX,\n        deltaY,\n        dir,\n        event,\n        first: state.first,\n        initial: state.initial,\n        velocity,\n        vxvy,\n      };\n\n      // call onSwipeStart if present and is first swipe event\n      eventData.first && props.onSwipeStart && props.onSwipeStart(eventData);\n\n      // call onSwiping if present\n      props.onSwiping && props.onSwiping(eventData);\n\n      // track if a swipe is cancelable (handler for swiping or swiped(dir) exists)\n      // so we can call preventDefault if needed\n      let cancelablePageSwipe = false;\n      if (\n        props.onSwiping ||\n        props.onSwiped ||\n        props[`onSwiped${dir}` as keyof SwipeableDirectionCallbacks]\n      ) {\n        cancelablePageSwipe = true;\n      }\n\n      if (\n        cancelablePageSwipe &&\n        props.preventScrollOnSwipe &&\n        props.trackTouch &&\n        event.cancelable\n      ) {\n        event.preventDefault();\n      }\n\n      return {\n        ...state,\n        // first is now always false\n        first: false,\n        eventData,\n        swiping: true,\n      };\n    });\n  };\n\n  const onEnd = (event: HandledEvents) => {\n    set((state, props) => {\n      let eventData: SwipeEventData | undefined;\n      if (state.swiping && state.eventData) {\n        // if swipe is less than duration fire swiped callbacks\n        if (event.timeStamp - state.start < props.swipeDuration) {\n          eventData = { ...state.eventData, event };\n          props.onSwiped && props.onSwiped(eventData);\n\n          const onSwipedDir =\n            props[\n              `onSwiped${eventData.dir}` as keyof SwipeableDirectionCallbacks\n            ];\n          onSwipedDir && onSwipedDir(eventData);\n        }\n      } else {\n        props.onTap && props.onTap({ event });\n      }\n\n      props.onTouchEndOrOnMouseUp && props.onTouchEndOrOnMouseUp({ event });\n\n      return { ...state, ...initialState, eventData };\n    });\n  };\n\n  const cleanUpMouse = () => {\n    // safe to just call removeEventListener\n    document.removeEventListener(mouseMove, onMove);\n    document.removeEventListener(mouseUp, onUp);\n  };\n\n  const onUp = (e: HandledEvents) => {\n    cleanUpMouse();\n    onEnd(e);\n  };\n\n  /**\n   * The value of passive on touchMove depends on `preventScrollOnSwipe`:\n   * - true => { passive: false }\n   * - false => { passive: true } // Default\n   *\n   * NOTE: When preventScrollOnSwipe is true, we attempt to call preventDefault to prevent scroll.\n   *\n   * props.touchEventOptions can also be set for all touch event listeners,\n   * but for `touchmove` specifically when `preventScrollOnSwipe` it will\n   * supersede and force passive to false.\n   *\n   */\n  const attachTouch: AttachTouch = (el, props) => {\n    let cleanup = () => {};\n    if (el && el.addEventListener) {\n      const baseOptions = {\n        ...defaultProps.touchEventOptions,\n        ...props.touchEventOptions,\n      };\n      // attach touch event listeners and handlers\n      const tls: [\n        typeof touchStart | typeof touchMove | typeof touchEnd,\n        (e: HandledEvents) => void,\n        { passive: boolean }\n      ][] = [\n        [touchStart, onStart, baseOptions],\n        // preventScrollOnSwipe option supersedes touchEventOptions.passive\n        [\n          touchMove,\n          onMove,\n          {\n            ...baseOptions,\n            ...(props.preventScrollOnSwipe ? { passive: false } : {}),\n          },\n        ],\n        [touchEnd, onEnd, baseOptions],\n      ];\n      tls.forEach(([e, h, o]) => el.addEventListener(e, h, o));\n      // return properly scoped cleanup method for removing listeners, options not required\n      cleanup = () => tls.forEach(([e, h]) => el.removeEventListener(e, h));\n    }\n    return cleanup;\n  };\n\n  const onRef = (el: HTMLElement | null) => {\n    // \"inline\" ref functions are called twice on render, once with null then again with DOM element\n    // ignore null here\n    if (el === null) return;\n    set((state, props) => {\n      // if the same DOM el as previous just return state\n      if (state.el === el) return state;\n\n      const addState: { cleanUpTouch?: () => void } = {};\n      // if new DOM el clean up old DOM and reset cleanUpTouch\n      if (state.el && state.el !== el && state.cleanUpTouch) {\n        state.cleanUpTouch();\n        addState.cleanUpTouch = void 0;\n      }\n      // only attach if we want to track touch\n      if (props.trackTouch && el) {\n        addState.cleanUpTouch = attachTouch(el, props);\n      }\n\n      // store event attached DOM el for comparison, clean up, and re-attachment\n      return { ...state, el, ...addState };\n    });\n  };\n\n  // set ref callback to attach touch event listeners\n  const output: { ref: typeof onRef; onMouseDown?: typeof onStart } = {\n    ref: onRef,\n  };\n\n  // if track mouse attach mouse down listener\n  if (handlerProps.trackMouse) {\n    output.onMouseDown = onStart;\n  }\n\n  return [output, attachTouch];\n}\n\nfunction updateTransientState(\n  state: SwipeableState,\n  props: SwipeablePropsWithDefaultOptions,\n  previousProps: SwipeablePropsWithDefaultOptions,\n  attachTouch: AttachTouch\n) {\n  // if trackTouch is off or there is no el, then remove handlers if necessary and exit\n  if (!props.trackTouch || !state.el) {\n    if (state.cleanUpTouch) {\n      state.cleanUpTouch();\n    }\n\n    return {\n      ...state,\n      cleanUpTouch: undefined,\n    };\n  }\n\n  // trackTouch is on, so if there are no handlers attached, attach them and exit\n  if (!state.cleanUpTouch) {\n    return {\n      ...state,\n      cleanUpTouch: attachTouch(state.el, props),\n    };\n  }\n\n  // trackTouch is on and handlers are already attached, so if preventScrollOnSwipe changes value,\n  // remove and reattach handlers (this is required to update the passive option when attaching\n  // the handlers)\n  if (\n    props.preventScrollOnSwipe !== previousProps.preventScrollOnSwipe ||\n    props.touchEventOptions.passive !== previousProps.touchEventOptions.passive\n  ) {\n    state.cleanUpTouch();\n\n    return {\n      ...state,\n      cleanUpTouch: attachTouch(state.el, props),\n    };\n  }\n\n  return state;\n}\n\nexport function useSwipeable(options: SwipeableProps): SwipeableHandlers {\n  const { trackMouse } = options;\n  const transientState = React.useRef({ ...initialState });\n  const transientProps = React.useRef<SwipeablePropsWithDefaultOptions>({\n    ...defaultProps,\n  });\n\n  // track previous rendered props\n  const previousProps = React.useRef<SwipeablePropsWithDefaultOptions>({\n    ...transientProps.current,\n  });\n  previousProps.current = { ...transientProps.current };\n\n  // update current render props & defaults\n  transientProps.current = {\n    ...defaultProps,\n    ...options,\n  };\n  // Force defaults for config properties\n  let defaultKey: keyof ConfigurationOptions;\n  for (defaultKey in defaultProps) {\n    if (transientProps.current[defaultKey] === void 0) {\n      (transientProps.current[defaultKey] as any) = defaultProps[defaultKey];\n    }\n  }\n\n  const [handlers, attachTouch] = React.useMemo(\n    () =>\n      getHandlers(\n        (stateSetter) =>\n          (transientState.current = stateSetter(\n            transientState.current,\n            transientProps.current\n          )),\n        { trackMouse }\n      ),\n    [trackMouse]\n  );\n\n  transientState.current = updateTransientState(\n    transientState.current,\n    transientProps.current,\n    previousProps.current,\n    attachTouch\n  );\n\n  return handlers;\n}\n"], "mappings": ";;;;;;;;;IAEa,OAAO;IACP,QAAQ;IACR,KAAK;IACL,OAAO;ACiCpB,IAAM,eAAqC;EACzC,OAAO;EACP,sBAAsB;EACtB,eAAe;EACf,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,mBAAmB,EAAE,SAAS,KAAI;;AAEpC,IAAM,eAA+B;EACnC,OAAO;EACP,SAAS,CAAC,GAAG,CAAC;EACd,OAAO;EACP,SAAS;EACT,IAAI,CAAC,GAAG,CAAC;;AAEX,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,aAAa;AAEnB,SAAS,aACP,MACA,MACA,QACA,QAAc;AAEd,MAAI,OAAO,MAAM;AACf,QAAI,SAAS,GAAG;AACd,aAAO;;AAET,WAAO;aACE,SAAS,GAAG;AACrB,WAAO;;AAET,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAc,OAAa;AAClD,MAAI,UAAU;AAAG,WAAO;AACxB,QAAM,iBAAkB,KAAK,KAAK,MAAO;AACzC,QAAM,IACJ,IAAI,CAAC,IAAI,KAAK,IAAI,cAAc,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,cAAc;AACtE,QAAM,IACJ,IAAI,CAAC,IAAI,KAAK,IAAI,cAAc,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,cAAc;AACtE,SAAO,CAAC,GAAG,CAAC;AACd;AAEA,SAAS,YACP,KACA,cAAiD;AAQjD,QAAM,UAAU,CAAC,UAAoB;AACnC,UAAM,UAAU,aAAa;AAE7B,QAAI,WAAW,MAAM,QAAQ,SAAS;AAAG;AAEzC,QAAI,CAAC,OAAO,UAAK;AAEf,UAAI,MAAM,cAAc,CAAC,SAAS;AAChC,iBAAS,iBAAiB,WAAW,MAAM;AAC3C,iBAAS,iBAAiB,SAAS,IAAI;;AAEzC,YAAM,EAAE,SAAS,QAAO,IAAK,UAAU,MAAM,QAAQ,CAAC,IAAI;AAC1D,YAAM,KAAK,gBAAgB,CAAC,SAAS,OAAO,GAAG,MAAM,aAAa;AAElE,YAAM,6BACJ,MAAM,0BAA0B,EAAE,MAAK,CAAE;AAE3C,aAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GACL,YAAY,GAAA,EACf,SAAS,GAAG,MAAK,GACjB,IACA,OAAO,MAAM,aAAa,EAAC,CAAA;KAE9B;;AAGH,QAAM,SAAS,CAAC,UAAoB;AAClC,QAAI,CAAC,OAAO,UAAK;AACf,YAAM,UAAU,aAAa;AAG7B,UAAI,WAAW,MAAM,QAAQ,SAAS,GAAG;AACvC,eAAO;;AAIT,UAAI,MAAM,YAAY,MAAM,QAAQ,MAAM,eAAe;AACvD,eAAO,MAAM,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,KAAK,GAAA,EAAE,SAAS,MAAK,CAAA,IAAK;;AAGxD,YAAM,EAAE,SAAS,QAAO,IAAK,UAAU,MAAM,QAAQ,CAAC,IAAI;AAC1D,YAAM,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,SAAS,OAAO,GAAG,MAAM,aAAa;AACtE,YAAM,SAAS,IAAI,MAAM,GAAG,CAAC;AAC7B,YAAM,SAAS,IAAI,MAAM,GAAG,CAAC;AAC7B,YAAM,OAAO,KAAK,IAAI,MAAM;AAC5B,YAAM,OAAO,KAAK,IAAI,MAAM;AAC5B,YAAM,QAAQ,MAAM,aAAa,KAAK,MAAM;AAC5C,YAAM,WAAW,KAAK,KAAK,OAAO,OAAO,OAAO,IAAI,KAAK,QAAQ;AACjE,YAAM,OAAgB,CAAC,UAAU,QAAQ,IAAI,UAAU,QAAQ,EAAE;AAEjE,YAAM,MAAM,aAAa,MAAM,MAAM,QAAQ,MAAM;AAGnD,YAAM,QACJ,OAAO,MAAM,UAAU,WACnB,MAAM,QACN,MAAM,MAAM,IAAI,YAAW,CAAgC,KAC3D,aAAa;AACnB,UAAI,OAAO,SAAS,OAAO,SAAS,CAAC,MAAM;AAAS,eAAO;AAE3D,YAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA,OAAO,MAAM;QACb,SAAS,MAAM;QACf;QACA;;AAIF,gBAAU,SAAS,MAAM,gBAAgB,MAAM,aAAa,SAAS;AAGrE,YAAM,aAAa,MAAM,UAAU,SAAS;AAI5C,UAAI,sBAAsB;AAC1B,UACE,MAAM,aACN,MAAM,YACN,MAAM,WAAW,GAAG,EAAuC,GAC3D;AACA,8BAAsB;;AAGxB,UACE,uBACA,MAAM,wBACN,MAAM,cACN,MAAM,YACN;AACA,cAAM,eAAc;;AAGtB,aAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA;;QAER,OAAO;QACP;QACA,SAAS;MAAI,CAAA;KAEhB;;AAGH,QAAM,QAAQ,CAAC,UAAoB;AACjC,QAAI,CAAC,OAAO,UAAK;AACf,UAAI;AACJ,UAAI,MAAM,WAAW,MAAM,WAAW;AAEpC,YAAI,MAAM,YAAY,MAAM,QAAQ,MAAM,eAAe;AACvD,sBAAS,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,MAAM,SAAS,GAAA,EAAE,MAAK,CAAA;AACvC,gBAAM,YAAY,MAAM,SAAS,SAAS;AAE1C,gBAAM,cACJ,MACE,WAAW,UAAU,GAAG,EAAuC;AAEnE,yBAAe,YAAY,SAAS;;aAEjC;AACL,cAAM,SAAS,MAAM,MAAM,EAAE,MAAK,CAAE;;AAGtC,YAAM,yBAAyB,MAAM,sBAAsB,EAAE,MAAK,CAAE;AAEpE,aAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,KAAK,GAAK,YAAY,GAAA,EAAE,UAAS,CAAA;KAC9C;;AAGH,QAAM,eAAe,MAAA;AAEnB,aAAS,oBAAoB,WAAW,MAAM;AAC9C,aAAS,oBAAoB,SAAS,IAAI;;AAG5C,QAAM,OAAO,CAAC,MAAgB;AAC5B,iBAAY;AACZ,UAAM,CAAC;;AAeT,QAAM,cAA2B,CAAC,IAAI,UAAK;AACzC,QAAI,UAAU,MAAA;IAAA;AACd,QAAI,MAAM,GAAG,kBAAkB;AAC7B,YAAM,cAAW,OAAA,OAAA,OAAA,OAAA,CAAA,GACZ,aAAa,iBAAiB,GAC9B,MAAM,iBAAiB;AAG5B,YAAM,MAIA;QACJ,CAAC,YAAY,SAAS,WAAW;;QAEjC;UACE;UACA;0CAEK,WAAW,GACV,MAAM,uBAAuB,EAAE,SAAS,MAAK,IAAK,CAAA,CAAE;;QAG5D,CAAC,UAAU,OAAO,WAAW;;AAE/B,UAAI,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,iBAAiB,GAAG,GAAG,CAAC,CAAC;AAEvD,gBAAU,MAAM,IAAI,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,oBAAoB,GAAG,CAAC,CAAC;;AAEtE,WAAO;;AAGT,QAAM,QAAQ,CAAC,OAAsB;AAGnC,QAAI,OAAO;AAAM;AACjB,QAAI,CAAC,OAAO,UAAK;AAEf,UAAI,MAAM,OAAO;AAAI,eAAO;AAE5B,YAAM,WAA0C,CAAA;AAEhD,UAAI,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,cAAc;AACrD,cAAM,aAAY;AAClB,iBAAS,eAAe;;AAG1B,UAAI,MAAM,cAAc,IAAI;AAC1B,iBAAS,eAAe,YAAY,IAAI,KAAK;;AAI/C,aAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,KAAK,GAAA,EAAE,GAAE,CAAA,GAAK,QAAQ;KACnC;;AAIH,QAAM,SAA8D;IAClE,KAAK;;AAIP,MAAI,aAAa,YAAY;AAC3B,WAAO,cAAc;;AAGvB,SAAO,CAAC,QAAQ,WAAW;AAC7B;AAEA,SAAS,qBACP,OACA,OACA,eACA,aAAwB;AAGxB,MAAI,CAAC,MAAM,cAAc,CAAC,MAAM,IAAI;AAClC,QAAI,MAAM,cAAc;AACtB,YAAM,aAAY;;AAGpB,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA,EACR,cAAc,OAAS,CAAA;;AAK3B,MAAI,CAAC,MAAM,cAAc;AACvB,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA,EACR,cAAc,YAAY,MAAM,IAAI,KAAK,EAAC,CAAA;;AAO9C,MACE,MAAM,yBAAyB,cAAc,wBAC7C,MAAM,kBAAkB,YAAY,cAAc,kBAAkB,SACpE;AACA,UAAM,aAAY;AAElB,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA,EACR,cAAc,YAAY,MAAM,IAAI,KAAK,EAAC,CAAA;;AAI9C,SAAO;AACT;SAEgB,aAAa,SAAuB;AAClD,QAAM,EAAE,WAAU,IAAK;AACvB,QAAM,iBAAuB,aAAM,OAAA,OAAA,CAAA,GAAM,YAAY,CAAA;AACrD,QAAM,iBAAuB,aAAM,OAAA,OAAA,CAAA,GAC9B,YAAY,CAAA;AAIjB,QAAM,gBAAsB,aAAM,OAAA,OAAA,CAAA,GAC7B,eAAe,OAAO,CAAA;AAE3B,gBAAc,UAAO,OAAA,OAAA,CAAA,GAAQ,eAAe,OAAO;AAGnD,iBAAe,UAAO,OAAA,OAAA,OAAA,OAAA,CAAA,GACjB,YAAY,GACZ,OAAO;AAGZ,MAAI;AACJ,OAAK,cAAc,cAAc;AAC/B,QAAI,eAAe,QAAQ,UAAU,MAAM,QAAQ;AAChD,qBAAe,QAAQ,UAAU,IAAY,aAAa,UAAU;;;AAIzE,QAAM,CAAC,UAAU,WAAW,IAAU,cACpC,MACE,YACE,CAAC,gBACE,eAAe,UAAU,YACxB,eAAe,SACf,eAAe,OAAO,GAE1B,EAAE,WAAU,CAAE,GAElB,CAAC,UAAU,CAAC;AAGd,iBAAe,UAAU,qBACvB,eAAe,SACf,eAAe,SACf,cAAc,SACd,WAAW;AAGb,SAAO;AACT;", "names": []}