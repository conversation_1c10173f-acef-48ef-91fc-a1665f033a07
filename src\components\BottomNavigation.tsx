import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

const BottomNavigation: React.FC = () => {
  const location = useLocation();
  
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <motion.div 
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      className="fixed bottom-0 left-0 right-0 bg-white leaf-shadow rounded-t-3xl py-2 px-4 z-40"
    >
      <div className="flex justify-around items-center">
        <NavItem 
          to="/home" 
          icon="bi-house-fill" 
          label="Garden" 
          isActive={isActive('/home')} 
        />
        
        <NavItem 
          to="/matches" 
          icon="bi-heart-fill" 
          label="Matches" 
          isActive={isActive('/matches')} 
        />
        
        <NavItem 
          to="/garden-party" 
          icon="bi-people-fill" 
          label="Party" 
          isActive={isActive('/garden-party')} 
        />
        
        <NavItem 
          to="/wisdom-tree" 
          icon="bi-tree-fill" 
          label="Wisdom" 
          isActive={isActive('/wisdom-tree')} 
        />
        
        <NavItem 
          to="/profile" 
          icon="bi-person-fill" 
          label="Profile" 
          isActive={isActive('/profile')} 
        />
      </div>
    </motion.div>
  );
};

interface NavItemProps {
  to: string;
  icon: string;
  label: string;
  isActive: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, isActive }) => {
  return (
    <Link to={to} className="flex flex-col items-center relative">
      <div className={`p-2 rounded-full ${isActive ? 'text-apple-red-500' : 'text-eden-green-500'}`}>
        <i className={`bi ${icon} text-2xl`}></i>
        {isActive && (
          <motion.div
            layoutId="activeTab"
            className="absolute inset-0 bg-eden-green-50 rounded-full -z-10"
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          />
        )}
      </div>
      <span className={`text-xs mt-1 ${isActive ? 'font-medium text-apple-red-500' : 'text-eden-green-700'}`}>
        {label}
      </span>
    </Link>
  );
};

export default BottomNavigation;
