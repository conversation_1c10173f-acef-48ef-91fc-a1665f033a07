import React from 'react';
import { motion } from 'framer-motion';
import Button from './Button';

interface EventCardProps {
  event: {
    id: string;
    title: string;
    date: Date;
    description: string;
    image: string;
    participants: number;
    isPremium: boolean;
  };
  onJoin: (eventId: string) => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, onJoin }) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      className="bg-white rounded-2xl overflow-hidden leaf-shadow mb-6"
    >
      <div className="relative h-48">
        <img 
          src={event.image} 
          alt={event.title} 
          className="w-full h-full object-cover"
          crossOrigin="anonymous"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <h3 className="text-xl font-bold text-white">{event.title}</h3>
          <p className="text-white/80 flex items-center">
            <i className="bi bi-calendar-event mr-2"></i>
            {event.date.toLocaleDateString(undefined, { 
              weekday: 'long', 
              month: 'short', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
        
        {event.isPremium && (
          <div className="absolute top-4 right-4 bg-eden-gold-500 text-eden-green-900 px-3 py-1 rounded-full text-sm font-medium">
            Premium
          </div>
        )}
      </div>
      
      <div className="p-4">
        <p className="text-eden-green-700 mb-4">{event.description}</p>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <i className="bi bi-people-fill text-eden-green-500 mr-2"></i>
            <span className="text-eden-green-700">{event.participants} participants</span>
          </div>
          
          {event.isPremium && (
            <div className="flex items-center text-eden-gold-500">
              <i className="bi bi-star-fill mr-1"></i>
              <span className="text-sm">Secret Garden Event</span>
            </div>
          )}
        </div>
        
        <Button
          variant={event.isPremium ? 'gold' : 'green'}
          fullWidth
          onClick={() => onJoin(event.id)}
          icon={event.isPremium ? 'bi-key-fill' : 'bi-calendar-check'}
        >
          {event.isPremium ? 'Unlock Event' : 'Join Event'}
        </Button>
      </div>
    </motion.div>
  );
};

export default EventCard;
