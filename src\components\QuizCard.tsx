import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Button from './Button';

interface QuizCardProps {
  quiz: {
    id: string;
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  };
  onComplete: (isCorrect: boolean) => void;
}

const QuizCard: React.FC<QuizCardProps> = ({ quiz, onComplete }) => {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const handleSubmit = () => {
    if (selectedOption !== null) {
      setIsSubmitted(true);
      onComplete(selectedOption === quiz.correctAnswer);
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-white rounded-2xl p-6 leaf-shadow"
    >
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 rounded-full bg-eden-green-100 flex items-center justify-center mr-3">
          <i className="bi bi-tree text-eden-green-600 text-xl"></i>
        </div>
        <h3 className="text-xl font-semibold text-eden-green-800">Tree of Knowledge</h3>
      </div>
      
      <h4 className="text-lg font-medium text-eden-green-900 mb-4">{quiz.question}</h4>
      
      <div className="space-y-3 mb-6">
        {quiz.options.map((option, index) => (
          <motion.div
            key={index}
            whileTap={{ scale: 0.98 }}
            onClick={() => !isSubmitted && setSelectedOption(index)}
            className={`
              p-4 rounded-xl border-2 cursor-pointer transition-all
              ${selectedOption === index 
                ? isSubmitted 
                  ? index === quiz.correctAnswer 
                    ? 'border-eden-green-500 bg-eden-green-50' 
                    : 'border-apple-red-500 bg-apple-red-50'
                  : 'border-eden-green-500 bg-eden-green-50'
                : 'border-eden-green-200 hover:border-eden-green-300'}
            `}
          >
            <div className="flex items-center">
              <div className={`
                w-6 h-6 rounded-full flex items-center justify-center mr-3
                ${selectedOption === index 
                  ? isSubmitted 
                    ? index === quiz.correctAnswer 
                      ? 'bg-eden-green-500 text-white' 
                      : 'bg-apple-red-500 text-white'
                    : 'bg-eden-green-500 text-white'
                  : 'bg-eden-green-100 text-eden-green-500'}
              `}>
                {isSubmitted && selectedOption === index ? (
                  index === quiz.correctAnswer ? (
                    <i className="bi bi-check"></i>
                  ) : (
                    <i className="bi bi-x"></i>
                  )
                ) : (
                  String.fromCharCode(65 + index) // A, B, C, D...
                )}
              </div>
              <span className="text-eden-green-800">{option}</span>
            </div>
          </motion.div>
        ))}
      </div>
      
      {isSubmitted && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className={`p-4 rounded-xl mb-6 ${selectedOption === quiz.correctAnswer ? 'bg-eden-green-50' : 'bg-apple-red-50'}`}
        >
          <div className="flex items-start">
            <div className={`
              w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5
              ${selectedOption === quiz.correctAnswer ? 'bg-eden-green-500 text-white' : 'bg-apple-red-500 text-white'}
            `}>
              <i className={`bi ${selectedOption === quiz.correctAnswer ? 'bi-lightbulb' : 'bi-info'}`}></i>
            </div>
            <div>
              <h5 className={`font-medium mb-1 ${selectedOption === quiz.correctAnswer ? 'text-eden-green-700' : 'text-apple-red-700'}`}>
                {selectedOption === quiz.correctAnswer ? 'Correct!' : 'Not quite right'}
              </h5>
              <p className={`text-sm ${selectedOption === quiz.correctAnswer ? 'text-eden-green-600' : 'text-apple-red-600'}`}>
                {quiz.explanation}
              </p>
            </div>
          </div>
        </motion.div>
      )}
      
      <Button
        variant={isSubmitted ? (selectedOption === quiz.correctAnswer ? 'green' : 'apple') : 'green'}
        fullWidth
        disabled={selectedOption === null}
        onClick={handleSubmit}
        icon={isSubmitted ? (selectedOption === quiz.correctAnswer ? 'bi-emoji-smile' : 'bi-emoji-neutral') : 'bi-check-circle'}
      >
        {isSubmitted ? (selectedOption === quiz.correctAnswer ? 'Wisdom Gained!' : 'Try Again Tomorrow') : 'Submit Answer'}
      </Button>
    </motion.div>
  );
};

export default QuizCard;
