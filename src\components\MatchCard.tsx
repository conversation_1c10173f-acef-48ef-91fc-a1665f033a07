import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

interface MatchCardProps {
  match: {
    id: string;
    name: string;
    image: string;
    lastMessage?: {
      text: string;
      timestamp: Date;
      isRead: boolean;
    };
  };
}

const MatchCard: React.FC<MatchCardProps> = ({ match }) => {
  return (
    <Link to={`/chat/${match.id}`}>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="flex items-center p-4 bg-white rounded-2xl leaf-shadow mb-3"
      >
        <div className="relative">
          <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-eden-green-200">
            <img 
              src={match.image} 
              alt={`${match.name}'s profile`} 
              className="w-full h-full object-cover"
              crossOrigin="anonymous"
            />
          </div>
          <div className="absolute -bottom-1 -right-1 bg-apple-red-500 rounded-full p-1">
            <i className="bi bi-apple text-white text-xs"></i>
          </div>
        </div>
        
        <div className="ml-4 flex-1">
          <div className="flex justify-between items-center">
            <h3 className="font-semibold text-eden-green-900">{match.name}</h3>
            {match.lastMessage && (
              <span className="text-xs text-eden-green-600">
                {new Date(match.lastMessage.timestamp).toLocaleDateString()}
              </span>
            )}
          </div>
          
          {match.lastMessage ? (
            <div className="flex justify-between items-center">
              <p className="text-sm text-eden-green-700 truncate max-w-[200px]">
                {match.lastMessage.text}
              </p>
              {!match.lastMessage.isRead && (
                <span className="w-3 h-3 bg-apple-red-500 rounded-full"></span>
              )}
            </div>
          ) : (
            <p className="text-sm text-eden-green-600 italic">
              Start a conversation
            </p>
          )}
        </div>
      </motion.div>
    </Link>
  );
};

export default MatchCard;
