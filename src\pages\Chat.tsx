import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import ChatBubble from '../components/ChatBubble';
import Button from '../components/Button';

// Mock data
const mockChats: Record<string, {
  user: {
    id: string;
    name: string;
    image: string;
    lastActive: Date;
  };
  messages: Array<{
    id: string;
    text: string;
    sender: 'user' | 'match';
    timestamp: Date;
    isRead: boolean;
  }>;
}> = {
  '1': {
    user: {
      id: '1',
      name: '<PERSON>',
      image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=300&q=80',
      lastActive: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
    },
    messages: [
      {
        id: '1',
        text: 'Hi there! I noticed we both love hiking. What\'s your favorite trail?',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        isRead: true
      },
      {
        id: '2',
        text: 'Hey <PERSON>! I love the Cascade Falls trail. Have you been there?',
        sender: 'user',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 23), // 23 hours ago
        isRead: true
      },
      {
        id: '3',
        text: 'I haven\'t, but it\'s on my list! I\'ve heard the views are amazing.',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 22), // 22 hours ago
        isRead: true
      },
      {
        id: '4',
        text: 'They really are! Maybe we could check it out together sometime?',
        sender: 'user',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        isRead: true
      },
      {
        id: '5',
        text: 'I would love to try that new café sometime!',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        isRead: false
      }
    ]
  },
  '2': {
    user: {
      id: '2',
      name: 'James',
      image: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?auto=format&fit=crop&w=300&q=80',
      lastActive: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
    },
    messages: [
      {
        id: '1',
        text: 'Hey, I see you\'re into photography too! What kind of camera do you use?',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
        isRead: true
      },
      {
        id: '2',
        text: 'Hi James! I use a Sony A7III. How about you?',
        sender: 'user',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 47), // 47 hours ago
        isRead: true
      },
      {
        id: '3',
        text: 'Nice! I have a Canon EOS R. Have you been to the botanical gardens for photos?',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        isRead: true
      },
      {
        id: '4',
        text: 'Not yet, but I\'ve been wanting to go. Is it good for portraits?',
        sender: 'user',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
        isRead: true
      },
      {
        id: '5',
        text: 'What are your plans for the weekend?',
        sender: 'match',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        isRead: true
      }
    ]
  }
};

// Serpent's Whisper (icebreakers)
const icebreakers = [
  "If you could add a new fruit to Eden, what would it be and why?",
  "What's your idea of a perfect day in paradise?",
  "If you could have dinner with any historical figure, who would it be?",
  "What's a 'forbidden pleasure' you secretly enjoy?",
  "If you were to write a book about your life, what would the title be?",
  "What's something you're passionate about that most people don't know?",
  "If you could have any superpower, what would it be and how would you use it?",
  "What's your favorite way to connect with nature?"
];

const Chat: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [chat, setChat] = useState(mockChats[id || '1']);
  const [newMessage, setNewMessage] = useState('');
  const [showIcebreakers, setShowIcebreakers] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    scrollToBottom();
  }, [chat?.messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const sendMessage = () => {
    if (!newMessage.trim()) return;
    
    const updatedChat = { ...chat };
    updatedChat.messages.push({
      id: `new-${Date.now()}`,
      text: newMessage,
      sender: 'user',
      timestamp: new Date(),
      isRead: false
    });
    
    setChat(updatedChat);
    setNewMessage('');
    
    // Simulate reply after 2 seconds
    setTimeout(() => {
      const replyChat = { ...updatedChat };
      replyChat.messages.push({
        id: `reply-${Date.now()}`,
        text: "That sounds great! I'd love to chat more about it.",
        sender: 'match',
        timestamp: new Date(),
        isRead: false
      });
      
      setChat(replyChat);
    }, 2000);
  };
  
  const useIcebreaker = (text: string) => {
    setNewMessage(text);
    setShowIcebreakers(false);
  };
  
  if (!chat) return <div>Chat not found</div>;
  
  return (
    <div className="min-h-screen bg-eden-green-50 flex flex-col">
      {/* Header */}
      <header className="bg-white py-3 px-4 flex items-center leaf-shadow">
        <Link to="/matches" className="mr-3">
          <i className="bi bi-arrow-left text-eden-green-700 text-xl"></i>
        </Link>
        
        <div className="flex items-center flex-1">
          <div className="relative">
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <img 
                src={chat.user.image} 
                alt={chat.user.name} 
                className="w-full h-full object-cover"
                crossOrigin="anonymous"
              />
            </div>
            
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-eden-green-500 rounded-full border-2 border-white"></div>
          </div>
          
          <div className="ml-3">
            <h2 className="font-semibold text-eden-green-900">{chat.user.name}</h2>
            <p className="text-xs text-eden-green-600">
              Active {new Date(chat.user.lastActive).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </p>
          </div>
        </div>
        
        <button className="w-8 h-8 rounded-full flex items-center justify-center text-eden-green-700">
          <i className="bi bi-three-dots-vertical"></i>
        </button>
      </header>
      
      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="max-w-lg mx-auto">
          {/* Match date */}
          <div className="text-center mb-6">
            <div className="inline-block px-3 py-1 bg-eden-green-100 rounded-full text-xs text-eden-green-700">
              You matched on {new Date(chat.messages[0].timestamp).toLocaleDateString()}
            </div>
          </div>
          
          {/* Messages */}
          {chat.messages.map(message => (
            <ChatBubble key={message.id} message={message} />
          ))}
          
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      {/* Input area */}
      <div className="bg-white p-4 leaf-shadow">
        <div className="max-w-lg mx-auto">
          {showIcebreakers && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 bg-eden-green-50 rounded-xl p-3"
            >
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <i className="bi bi-chat-heart text-eden-gold-500 mr-2"></i>
                  <h4 className="font-medium text-eden-green-800">Serpent's Whisper</h4>
                </div>
                <button 
                  onClick={() => setShowIcebreakers(false)}
                  className="text-eden-green-600"
                >
                  <i className="bi bi-x"></i>
                </button>
              </div>
              
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {icebreakers.map((text, index) => (
                  <button
                    key={index}
                    onClick={() => useIcebreaker(text)}
                    className="w-full text-left p-2 rounded-lg hover:bg-eden-green-100 text-eden-green-800 text-sm transition-colors"
                  >
                    {text}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
          
          <div className="flex items-end">
            <button 
              onClick={() => setShowIcebreakers(!showIcebreakers)}
              className="w-10 h-10 rounded-full flex items-center justify-center text-eden-green-600 bg-eden-green-50 mr-2"
            >
              <i className="bi bi-lightbulb"></i>
            </button>
            
            <div className="flex-1 relative">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type a message..."
                className="w-full py-3 px-4 pr-12 bg-eden-green-50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-eden-green-300 resize-none"
                rows={1}
                style={{ minHeight: '46px', maxHeight: '120px' }}
              />
              
              <button className="absolute right-3 bottom-3 text-eden-green-500">
                <i className="bi bi-emoji-smile"></i>
              </button>
            </div>
            
            <button 
              onClick={sendMessage}
              disabled={!newMessage.trim()}
              className={`w-10 h-10 rounded-full flex items-center justify-center ml-2 ${newMessage.trim() ? 'bg-apple-red-500 text-white' : 'bg-eden-green-200 text-eden-green-400'}`}
            >
              <i className="bi bi-send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chat;
