import React from 'react';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

interface ChatBubbleProps {
  message: {
    id: string;
    text: string;
    sender: 'user' | 'match';
    timestamp: Date;
    isRead: boolean;
  };
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div
        className={`
          max-w-[80%] rounded-2xl px-4 py-3
          ${isUser 
            ? 'bg-eden-green-500 text-white rounded-br-none' 
            : 'bg-white leaf-shadow rounded-bl-none'}
        `}
      >
        <p className={`${isUser ? 'text-white' : 'text-eden-green-900'}`}>
          {message.text}
        </p>
        <div className={`text-xs mt-1 flex justify-between items-center ${isUser ? 'text-white/70' : 'text-eden-green-600'}`}>
          <span>{formatDistanceToNow(message.timestamp, { addSuffix: true })}</span>
          {isUser && (
            <span>
              {message.isRead ? (
                <i className="bi bi-check-all ml-1"></i>
              ) : (
                <i className="bi bi-check ml-1"></i>
              )}
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ChatBubble;
