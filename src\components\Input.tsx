import React from 'react';

interface InputProps {
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  id?: string;
  required?: boolean;
  label?: string;
  error?: string;
  icon?: string;
  className?: string;
}

const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  name,
  id,
  required = false,
  label,
  error,
  icon,
  className = ''
}) => {
  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label 
          htmlFor={id || name} 
          className="block text-eden-green-700 font-medium mb-2"
        >
          {label}
          {required && <span className="text-apple-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i className={`bi ${icon} text-eden-green-400`}></i>
          </div>
        )}
        
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          name={name}
          id={id || name}
          required={required}
          className={`
            w-full py-3 px-4 ${icon ? 'pl-10' : ''}
            bg-white border-2 rounded-xl
            focus:outline-none focus:ring-2 focus:ring-eden-green-300
            transition-all duration-200
            ${error ? 'border-apple-red-400' : 'border-eden-green-200 focus:border-eden-green-400'}
          `}
        />
      </div>
      
      {error && (
        <p className="mt-1 text-apple-red-500 text-sm">{error}</p>
      )}
    </div>
  );
};

export default Input;
