import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import Confetti from 'react-confetti';

interface MatchAnimationProps {
  matchedUser: {
    name: string;
    image: string;
  };
  onClose: () => void;
}

const MatchAnimation: React.FC<MatchAnimationProps> = ({ matchedUser, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);

    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="match-animation"
    >
      <Confetti
        width={window.innerWidth}
        height={window.innerHeight}
        recycle={false}
        numberOfPieces={200}
        colors={['#65ae65', '#f83b3b', '#efb506', '#b58455']}
      />
      
      <div className="text-center px-6">
        <motion.div
          initial={{ scale: 0.5, y: 50 }}
          animate={{ scale: 1, y: 0 }}
          transition={{ 
            type: "spring",
            stiffness: 300,
            damping: 15,
            delay: 0.2
          }}
          className="mb-6"
        >
          <h2 className="text-4xl font-bold text-white mb-2 font-eden">It's a Match!</h2>
          <p className="text-xl text-white/80">You and {matchedUser.name} have shared the forbidden fruit</p>
        </motion.div>
        
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="flex justify-center items-center gap-4 mb-8"
        >
          <div className="relative">
            <motion.div
              animate={{ 
                rotate: [0, -10, 10, -5, 0],
                x: [0, -5, 5, -2, 0]
              }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="w-32 h-32 rounded-full overflow-hidden border-4 border-eden-green-300"
            >
              <img 
                src="https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?auto=format&fit=crop&w=300&q=80" 
                alt="Your profile" 
                className="w-full h-full object-cover"
                crossOrigin="anonymous"
              />
            </motion.div>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8 }}
              className="absolute -bottom-2 -right-2 bg-apple-red-500 rounded-full p-2"
            >
              <i className="bi bi-apple text-white text-xl"></i>
            </motion.div>
          </div>
          
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ 
              duration: 1,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="text-apple-red-500 text-4xl"
          >
            <i className="bi bi-heart-fill"></i>
          </motion.div>
          
          <div className="relative">
            <motion.div
              animate={{ 
                rotate: [0, 10, -10, 5, 0],
                x: [0, 5, -5, 2, 0]
              }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="w-32 h-32 rounded-full overflow-hidden border-4 border-eden-green-300"
            >
              <img 
                src={matchedUser.image} 
                alt={`${matchedUser.name}'s profile`} 
                className="w-full h-full object-cover"
                crossOrigin="anonymous"
              />
            </motion.div>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8 }}
              className="absolute -bottom-2 -right-2 bg-apple-red-500 rounded-full p-2"
            >
              <i className="bi bi-apple text-white text-xl"></i>
            </motion.div>
          </div>
        </motion.div>
        
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="flex flex-col gap-3"
        >
          <button 
            onClick={onClose}
            className="apple-button"
          >
            <i className="bi bi-chat-heart-fill mr-2"></i>
            Send a Message
          </button>
          
          <button 
            onClick={onClose}
            className="py-3 px-6 text-white/80 font-medium"
          >
            Keep Exploring
          </button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default MatchAnimation;
