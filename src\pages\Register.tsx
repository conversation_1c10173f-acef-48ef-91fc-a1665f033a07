import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Input from '../components/Input';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const Register: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [userType, setUserType] = useState<'adam' | 'eve' | 'garden-spirit'>('adam');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1);
  
  const { register } = useAuth();
  const navigate = useNavigate();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (step === 1) {
      setStep(2);
      return;
    }
    
    setError('');
    setIsLoading(true);
    
    try {
      await register(email, password, name, userType);
      navigate('/home');
    } catch (err) {
      setError('Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Input from '../components/Input';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const Register: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [userType, setUserType] = useState<'adam' | 'eve' | 'garden-spirit'>('adam');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1);

  const { register } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (step === 1) {
      setStep(2);
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      await register(email, password, name, userType);
      navigate('/home');
    } catch (err) {
      setError('Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Background with garden pattern */}
      <div className="fixed inset-0 leaf-pattern opacity-20 z-0"></div>

      {/* Content */}
      <div className="flex-1 flex flex-col relative z-10">
        {/* Back button */}
        <header className="pt-6 px-6">
          {step === 1 ? (
            <Link to="/" className="inline-flex items-center text-eden-green-700 hover:text-eden-green-800">
              <i className="bi bi-arrow-left text-xl mr-2"></i>
              <span>Back</span>
            </Link>
          ) : (
            <button 
              onClick={() => setStep(1)}
              className="inline-flex items-center text-eden-green-700 hover:text-eden-green-800"
            >
              <i className="bi bi-arrow-left text-xl mr-2"></i>
              <span>Back</span>
            </button>
          )}
        </header>
        
        {/* Main content */}
        <main className="flex-1 flex flex-col items-center justify-center px-6 py-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <i className="bi bi-apple text-apple-red-500 text-4xl"></i>
              </div>
              <h2 className="text-3xl font-bold text-eden-green-800 mb-2 font-eden">
                {step === 1 ? 'Join the Garden' : 'Choose Your Identity'}
              </h2>
              <p className="text-eden-green-700">
                {step === 1 
                  ? 'Create your account to start your journey' 
                  : 'How would you like to be known in Eden?'}
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 leaf-shadow">
              {error && (
                <div className="mb-4 p-3 bg-apple-red-50 border border-apple-red-200 rounded-lg text-apple-red-700 text-sm">
                  <div className="flex items-center">
                    <i className="bi bi-exclamation-circle mr-2"></i>
                    <span>{error}</span>
                  </div>
                </div>
              )}
              
              <form onSubmit={handleSubmit}>
                {step === 1 ? (
                  <>
                    <Input
                      label="Name"
                      placeholder="Your name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                      icon="bi-person"
                    />
                    
                    <Input
                      type="email"
                      label="Email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      icon="bi-envelope"
                    />
                    
                    <Input
                      type="password"
                      label="Password"
                      placeholder="Create a password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      icon="bi-lock"
                    />
                  </>
                ) : (
                  <div className="space-y-4 mb-6">
                    <label className="block text-eden-green-700 font-medium mb-2">
                      I identify as:
                    </label>
                    
                    <div 
                      onClick={() => setUserType('adam')}
                      className={`
                        p-4 rounded-xl border-2 cursor-pointer transition-all flex items-center
                        ${userType === 'adam' 
                          ? 'border-eden-earth-500 bg-eden-earth-50' 
                          : 'border-eden-green-200 hover:border-eden-green-300'}
                      `}
                    >
                      <div className={`
                        w-6 h-6 rounded-full flex items-center justify-center mr-3
                        ${userType === 'adam' 
                          ? 'bg-eden-earth-500 text-white' 
                          : 'bg-eden-green-100 text-eden-green-500'}
                      `}>
                        <i className="bi bi-gender-male"></i>
                      </div>
                      <span className="text-eden-green-800 font-medium">Adam</span>
                    </div>
                    
                    <div 
                      onClick={() => setUserType('eve')}
                      className={`
                        p-4 rounded-xl border-2 cursor-pointer transition-all flex items-center
                        ${userType === 'eve' 
                          ? 'border-apple-red-500 bg-apple-red-50' 
                          : 'border-eden-green-200 hover:border-eden-green-300'}
                      `}
                    >
                      <div className={`
                        w-6 h-6 rounded-full flex items-center justify-center mr-3
                        ${userType === 'eve' 
                          ? 'bg-apple-red-500 text-white' 
                          : 'bg-eden-green-100 text-eden-green-500'}
                      `}>
                        <i className="bi bi-gender-female"></i>
                      </div>
                      <span className="text-eden-green-800 font-medium">Eve</span>
                    </div>
                    
                    <div 
                      onClick={() => setUserType('garden-spirit')}
                      className={`
                        p-4 rounded-xl border-2 cursor-pointer transition-all flex items-center
                        ${userType === 'garden-spirit' 
                          ? 'border-eden-gold-500 bg-eden-gold-50' 
                          : 'border-eden-green-200 hover:border-eden-green-300'}
                      `}
                    >
                      <div className={`
                        w-6 h-6 rounded-full flex items-center justify-center mr-3
                        ${userType === 'garden-spirit' 
                          ? 'bg-eden-gold-500 text-eden-green-900' 
                          : 'bg-eden-green-100 text-eden-green-500'}
                      `}>
                        <i className="bi bi-asterisk"></i>
                      </div>
                      <span className="text-eden-green-800 font-medium">Garden Spirit</span>
                    </div>
                  </div>
                )}
                
                <Button
                  type="submit"
                  variant={step === 1 ? 'green' : 'apple'}
                  fullWidth
                  disabled={isLoading}
                  icon={isLoading ? "bi-arrow-repeat" : step === 1 ? "bi-arrow-right" : "bi-check-lg"}
                >
                  {isLoading 
                    ? 'Creating Account...' 
                    : step === 1 
                      ? 'Continue' 
                      : 'Create Account'}
                </Button>
              </form>
            </div>
            
            <div className="text-center mt-6">
              <p className="text-eden-green-700">
                Already have an account?{' '}
                <Link to="/login" className="text-apple-red-500 font-medium hover:text-apple-red-600">
                  Sign in
                </Link>
              </p>
            </div>
          </motion.div>
        </main>
        
        {/* Footer */}
        <footer className="py-6 text-center text-eden-green-600 text-sm">
          <p>Designed by WebSparks AI</p>
        </footer>
      </div>
    </div>
  );
};

export default Register;
