import React, { useState } from 'react';
import { motion } from 'framer-motion';
import EventCard from '../components/EventCard';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

// Mock data
const mockEvents = [
  {
    id: '1',
    title: 'Eden Masquerade Ball',
    date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3), // 3 days from now
    description: 'Join our virtual masquerade ball where you can meet new people in a fun, themed environment. Dress up and show off your creative side!',
    image: 'https://images.unsplash.com/photo-1519671482749-fd09be7ccebf?auto=format&fit=crop&w=800&q=80',
    participants: 42,
    isPremium: false
  },
  {
    id: '2',
    title: 'Fruit Tasting Social',
    date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // 5 days from now
    description: 'A casual virtual gathering where everyone shares their favorite fruit and the story behind why they love it. A perfect way to break the ice!',
    image: 'https://images.unsplash.com/photo-1519996529931-28324d5a630e?auto=format&fit=crop&w=800&q=80',
    participants: 28,
    isPremium: false
  },
  {
    id: '3',
    title: 'Secret Garden Soirée',
    date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // 2 days from now
    description: 'An exclusive event for premium members. Join us for an intimate gathering with curated matches and special activities designed for deeper connections.',
    image: 'https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?auto=format&fit=crop&w=800&q=80',
    participants: 15,
    isPremium: true
  },
  {
    id: '4',
    title: 'Speed Dating Night',
    date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1), // Tomorrow
    description: 'Meet multiple potential matches in one evening with our structured speed dating format. Quick, fun, and efficient way to find your connection!',
    image: 'https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?auto=format&fit=crop&w=800&q=80',
    participants: 36,
    isPremium: false
  }
];

const GardenParty: React.FC = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState(mockEvents);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<typeof mockEvents[0] | null>(null);
  
  const handleJoinEvent = (eventId: string) => {
    const event = events.find(e => e.id === eventId);
    if (!event) return;
    
    if (event.isPremium && !user?.isPremium) {
      setSelectedEvent(event);
      setShowPremiumModal(true);
      return;
    }
    
    // In a real app, you would handle the event registration
    alert(`You've joined the ${event.title} event!`);
  };
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">Garden Party</h1>
        <p className="text-eden-green-600">Join events and meet new people</p>
      </header>
      
      {/* Featured event */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative h-48 rounded-2xl overflow-hidden mb-6 leaf-shadow"
      >
        <img 
          src="https://images.unsplash.com/photo-1496024840928-4c417adf211d?auto=format&fit=crop&w=800&q=80" 
          alt="Featured event" 
          className="w-full h-full object-cover"
          crossOrigin="anonymous"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="flex items-center mb-2">
            <div className="px-2 py-1 bg-eden-gold-500 text-eden-green-900 rounded-full text-xs font-medium mr-2">
              Featured
            </div>
            <div className="text-white/80 text-xs">
              Tomorrow, 8:00 PM
            </div>
          </div>
          
          <h2 className="text-xl font-bold text-white mb-1">Eden Garden Gala</h2>
          <p className="text-white/90 text-sm mb-3">
            Our biggest social event of the month with games, music, and more!
          </p>
          
          <Button
            variant="apple"
            size="sm"
            onClick={() => handleJoinEvent('featured')}
            icon="bi-calendar-check"
          >
            Join Event
          </Button>
        </div>
      </motion.div>
      
      {/* Upcoming events */}
      <h2 className="text-xl font-semibold text-eden-green-800 mb-4">Upcoming Events</h2>
      
      <div>
        {events.map(event => (
          <EventCard
            key={event.id}
            event={event}
            onJoin={handleJoinEvent}
          />
        ))}
      </div>
      
      {/* Premium modal */}
      {showPremiumModal && selectedEvent && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
          onClick={() => setShowPremiumModal(false)}
        >
          <motion.div
            initial={{ scale: 0.9, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            className="bg-white rounded-2xl p-6 max-w-md w-full"
            onClick={e => e.stopPropagation()}
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 rounded-full bg-eden-gold-100 flex items-center justify-center mx-auto mb-4">
                <i className="bi bi-key-fill text-eden-gold-500 text-2xl"></i>
              </div>
              
              <h3 className="text-xl font-bold text-eden-green-800 mb-2 font-eden">
                Unlock Secret Garden
              </h3>
              
              <p className="text-eden-green-700">
                This exclusive event is only available to premium members.
              </p>
            </div>
            
            <div className="bg-eden-green-50 rounded-xl p-4 mb-6">
              <h4 className="font-semibold text-eden-green-800 mb-2">
                {selectedEvent.title}
              </h4>
              
              <p className="text-sm text-eden-green-700 mb-2">
                {selectedEvent.description}
              </p>
              
              <div className="flex items-center text-eden-green-600 text-sm">
                <i className="bi bi-calendar-event mr-2"></i>
                <span>
                  {selectedEvent.date.toLocaleDateString(undefined, { 
                    weekday: 'long', 
                    month: 'short', 
                    day: 'numeric'
                  })}
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              <Button
                variant="gold"
                fullWidth
                icon="bi-unlock"
              >
                Upgrade to Premium
              </Button>
              
              <button 
                onClick={() => setShowPremiumModal(false)}
                className="w-full text-center py-2 text-eden-green-600"
              >
                Maybe Later
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default GardenParty;
