import React, { useState } from 'react';
import { motion } from 'framer-motion';
import QuizCard from '../components/QuizCard';
import Button from '../components/Button';

// Mock data
const mockQuizzes = [
  {
    id: '1',
    question: 'What is the most important quality in a relationship?',
    options: ['Trust', 'Communication', 'Passion', 'Shared interests'],
    correctAnswer: 1, // Communication
    explanation: 'While all these qualities are important, communication is the foundation that enables trust, helps navigate passion, and allows you to discover shared interests.'
  },
  {
    id: '2',
    question: 'Which of these is a sign of emotional intelligence?',
    options: ['Never showing vulnerability', 'Being able to name your emotions', 'Always staying positive', 'Avoiding difficult conversations'],
    correctAnswer: 1, // Being able to name your emotions
    explanation: 'Emotional intelligence involves recognizing and understanding your own emotions, which is the first step to managing them effectively in relationships.'
  },
  {
    id: '3',
    question: 'In the Garden of Eden story, what does the serpent symbolize?',
    options: ['Evil', 'Temptation', 'Knowledge', 'Deception'],
    correctAnswer: 2, // Knowledge
    explanation: 'While often associated with temptation, the serpent in many interpretations represents knowledge and wisdom - offering the fruit from the Tree of Knowledge.'
  }
];

const WisdomTree: React.FC = () => {
  const [currentQuizIndex, setCurrentQuizIndex] = useState(0);
  const [completedQuiz, setCompletedQuiz] = useState(false);
  const [quizResult, setQuizResult] = useState<boolean | null>(null);
  const [dailyTip, setDailyTip] = useState({
    title: 'Authentic Connections',
    content: 'Be genuine in your conversations. Authenticity creates stronger connections than trying to impress with a persona that is not truly you.'
  });
  
  const handleQuizComplete = (isCorrect: boolean) => {
    setQuizResult(isCorrect);
    setCompletedQuiz(true);
  };
  
  const resetQuiz = () => {
    setCurrentQuizIndex((currentQuizIndex + 1) % mockQuizzes.length);
    setCompletedQuiz(false);
    setQuizResult(null);
  };
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">Wisdom Tree</h1>
        <p className="text-eden-green-600">Grow your relationship knowledge</p>
      </header>
      
      {/* Daily wisdom */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-eden-gold-100 flex items-center justify-center mr-3">
            <i className="bi bi-lightbulb text-eden-gold-500"></i>
          </div>
          <h2 className="text-lg font-semibold text-eden-green-800">Daily Wisdom</h2>
        </div>
        
        <h3 className="font-medium text-eden-green-800 mb-2">{dailyTip.title}</h3>
        <p className="text-eden-green-700 mb-4">{dailyTip.content}</p>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-eden-green-500">New wisdom tomorrow</span>
          <button className="text-eden-green-600 hover:text-eden-green-700">
            <i className="bi bi-share"></i>
          </button>
        </div>
      </motion.div>
      
      {/* Daily quiz */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-eden-green-800">Daily Quiz</h2>
          
          {completedQuiz && (
            <button 
              onClick={resetQuiz}
              className="text-eden-green-600 hover:text-eden-green-700 flex items-center"
            >
              <i className="bi bi-arrow-repeat mr-1"></i>
              <span>Try Another</span>
            </button>
          )}
        </div>
        
        <QuizCard
          quiz={mockQuizzes[currentQuizIndex]}
          onComplete={handleQuizComplete}
        />
      </motion.div>
      
      {/* Reward section (shown after completing quiz) */}
      {completedQuiz && quizResult && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ delay: 0.3 }}
          className="mt-6 bg-white rounded-2xl p-6 leaf-shadow"
        >
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-eden-green-100 flex items-center justify-center mx-auto mb-4">
              <i className="bi bi-gift text-eden-green-500 text-2xl"></i>
            </div>
            
            <h3 className="text-lg font-semibold text-eden-green-800 mb-2">
              Wisdom Reward Unlocked!
            </h3>
            
            <p className="text-eden-green-700 mb-4">
              You have earned 5 extra apples to send today. Use them wisely to make meaningful connections!
            </p>
            
            <Button variant="green" icon="bi-apple">
              Claim 5 Apples
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default WisdomTree;
