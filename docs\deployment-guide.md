# <PERSON> & <PERSON> Dating App - Deployment Guide

B<PERSON> rehber, <PERSON> & <PERSON> Dating App'i Google Play Store'da yayın<PERSON>ak için gerekli adımları içerir.

## Ön Gereksinimler

### 1. Geliştirme Ortamı
- Node.js 20+ (Capacitor i<PERSON><PERSON> gere<PERSON>)
- Android Studio (Android build için)
- Xcode (iOS build için - sadece macOS)
- Git

### 2. Hesaplar
- Google Play Console Developer Account ($25 tek seferlik ücret)
- Apple Developer Account ($99/yıl - iOS için)
- Supabase Account (ücretsiz tier mevcut)

## 1. Supabase Backend Kurulumu

### Adım 1: Supabase Projesi Oluşturma
1. [Supabase Dashboard](https://app.supabase.com)'a gidin
2. "New Project" oluşturun
3. `scripts/setup-database.md` dosyasındaki adımları takip edin

### Adım 2: Environment Variables
```bash
# .env dosyasını oluşturun
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 2. PWA (Progressive Web App) Build

### Adım 1: Production Build
```bash
npm run build
```

### Adım 2: PWA Test
```bash
npm run preview
```

### Adım 3: PWA Deployment
- Vercel, Netlify, veya Firebase Hosting kullanabilirsiniz
- HTTPS zorunludur (PWA için)

## 3. Android App Build

### Ön Gereksinimler
- Node.js 20+ yükleyin
- Android Studio yükleyin
- Java 17+ yükleyin

### Adım 1: Capacitor Kurulumu
```bash
# Node.js 20+ gerekli
npm install @capacitor/core @capacitor/cli @capacitor/android
npx cap init
```

### Adım 2: Android Platform Ekleme
```bash
npx cap add android
```

### Adım 3: Build ve Sync
```bash
npm run build
npx cap copy android
npx cap open android
```

### Adım 4: Android Studio'da Build
1. Android Studio açılacak
2. "Build" > "Generate Signed Bundle / APK" seçin
3. Keystore oluşturun (ilk kez ise)
4. Release APK/AAB oluşturun

## 4. Google Play Store Yayınlama

### Adım 1: Google Play Console Hesabı
1. [Google Play Console](https://play.google.com/console)'a gidin
2. Developer hesabı oluşturun ($25 ücret)
3. Kimlik doğrulamasını tamamlayın

### Adım 2: Uygulama Oluşturma
1. "Create app" butonuna tıklayın
2. Uygulama detaylarını doldurun:
   - App name: "Adam & Eve Dating"
   - Default language: Turkish
   - App or game: App
   - Free or paid: Free

### Adım 3: Store Listing
```
Title: Adam & Eve Dating
Short description: Find your perfect match in the Garden of Eden
Full description: 
Adam & Eve is a unique dating app that brings together people looking for meaningful connections. With our innovative matching system, beautiful design, and focus on authentic relationships, find your perfect match in the Garden of Eden.

Features:
- Smart matching algorithm
- Beautiful, intuitive interface
- Real-time messaging
- Photo sharing
- Interest-based matching
- Safe and secure platform
```

### Adım 4: App Content
- Content rating: Dating apps için uygun rating seçin
- Target audience: 18+ (dating app olduğu için)
- Privacy policy: Gerekli (oluşturmanız gerekir)

### Adım 5: APK/AAB Upload
1. "App releases" bölümüne gidin
2. "Internal testing" veya "Production" seçin
3. AAB dosyasını upload edin
4. Release notes ekleyin

## 5. App Store Assets

### Gerekli Görseller
- App icon: 512x512 px
- Feature graphic: 1024x500 px
- Screenshots: En az 2 adet (phone ve tablet için)
- Privacy policy URL

### Screenshot Önerileri
1. Ana sayfa (profil kartları)
2. Eşleşme ekranı
3. Mesajlaşma ekranı
4. Profil düzenleme ekranı

## 6. Güvenlik ve Privacy

### Privacy Policy
Aşağıdaki konuları içermelidir:
- Hangi verilerin toplandığı
- Verilerin nasıl kullanıldığı
- Üçüncü taraf servisler (Supabase)
- Kullanıcı hakları
- İletişim bilgileri

### Data Protection
- GDPR compliance (EU kullanıcıları için)
- CCPA compliance (California kullanıcıları için)
- Kullanıcı verilerinin silinme hakkı

## 7. Testing

### Pre-launch Checklist
- [ ] Tüm özellikler çalışıyor
- [ ] Authentication sistemi test edildi
- [ ] Database bağlantıları test edildi
- [ ] Push notifications çalışıyor (varsa)
- [ ] Offline functionality test edildi
- [ ] Performance test edildi
- [ ] Security test edildi

### Test Devices
- Farklı Android versiyonları (API 21+)
- Farklı ekran boyutları
- Düşük RAM'li cihazlar

## 8. Launch Strategy

### Soft Launch
1. Internal testing ile başlayın
2. Closed testing (arkadaşlar, aile)
3. Open testing (sınırlı kullanıcı)
4. Production release

### Marketing
- App Store Optimization (ASO)
- Social media presence
- Landing page oluşturma
- Influencer partnerships

## 9. Post-Launch

### Monitoring
- Crash reports (Firebase Crashlytics)
- User analytics (Google Analytics)
- Performance monitoring
- User feedback tracking

### Updates
- Bug fixes
- New features
- Performance improvements
- Security updates

## 10. Monetization (Gelecek)

### Freemium Model
- Basic features ücretsiz
- Premium features ücretli:
  - Unlimited likes
  - Super likes
  - Profile boost
  - Advanced filters

### In-App Purchases
- Google Play Billing entegrasyonu
- Subscription management
- Revenue tracking

## Troubleshooting

### Yaygın Sorunlar

1. **Build Errors**
   - Node.js versiyonunu kontrol edin
   - Dependencies'i temizleyip yeniden yükleyin
   - Android SDK path'ini kontrol edin

2. **Upload Errors**
   - APK signing kontrolü
   - Target SDK version kontrolü
   - Permissions kontrolü

3. **Store Rejection**
   - Content policy violations
   - Technical issues
   - Metadata problems

### Faydalı Linkler
- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)
- [Android App Bundle Guide](https://developer.android.com/guide/app-bundle)
- [Capacitor Documentation](https://capacitorjs.com/docs)
- [Supabase Documentation](https://supabase.com/docs)

## Support

Deployment sürecinde sorun yaşarsanız:
1. Dokümantasyonu kontrol edin
2. Community forumlarını kontrol edin
3. GitHub issues oluşturun
4. Professional support alın
