import React, { useState } from 'react';
import { motion } from 'framer-motion';
import MatchCard from '../components/MatchCard';

// Mock data
const mockMatches = [
  {
    id: '1',
    name: '<PERSON>',
    image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=300&q=80',
    lastMessage: {
      text: 'I would love to try that new café sometime!',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      isRead: false
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    image: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?auto=format&fit=crop&w=300&q=80',
    lastMessage: {
      text: 'What are your plans for the weekend?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: true
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    image: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?auto=format&fit=crop&w=300&q=80',
    lastMessage: {
      text: 'That book recommendation was amazing! I just finished it.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isRead: true
    }
  },
  {
    id: '4',
    name: 'Ethan',
    image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?auto=format&fit=crop&w=300&q=80',
    lastMessage: {
      text: 'Have you ever tried rock climbing?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
      isRead: true
    }
  },
  {
    id: '5',
    name: 'Luna',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=300&q=80'
    // No last message
  }
];

const Matches: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'messages' | 'likes'>('messages');
  const [matches, setMatches] = useState(mockMatches);
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">Connections</h1>
      </header>
      
      {/* Tabs */}
      <div className="flex bg-white rounded-xl leaf-shadow mb-6">
        <button
          onClick={() => setActiveTab('messages')}
          className={`flex-1 py-3 text-center relative ${activeTab === 'messages' ? 'text-apple-red-500 font-medium' : 'text-eden-green-700'}`}
        >
          Messages
          {activeTab === 'messages' && (
            <motion.div
              layoutId="tabIndicator"
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-apple-red-500"
              transition={{ duration: 0.3 }}
            />
          )}
        </button>
        
        <button
          onClick={() => setActiveTab('likes')}
          className={`flex-1 py-3 text-center relative ${activeTab === 'likes' ? 'text-apple-red-500 font-medium' : 'text-eden-green-700'}`}
        >
          Likes
          {activeTab === 'likes' && (
            <motion.div
              layoutId="tabIndicator"
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-apple-red-500"
              transition={{ duration: 0.3 }}
            />
          )}
        </button>
      </div>
      
      {/* Content */}
      <AnimatedTabContent activeTab={activeTab} matches={matches} />
    </div>
  );
};

interface AnimatedTabContentProps {
  activeTab: 'messages' | 'likes';
  matches: typeof mockMatches;
}

const AnimatedTabContent: React.FC<AnimatedTabContentProps> = ({ activeTab, matches }) => {
  return (
    <motion.div
      key={activeTab}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      {activeTab === 'messages' ? (
        matches.length > 0 ? (
          <div>
            {matches.map(match => (
              <MatchCard key={match.id} match={match} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="flex justify-center mb-4">
              <i className="bi bi-chat text-eden-green-300 text-5xl"></i>
            </div>
            <h3 className="text-xl font-semibold text-eden-green-800 mb-2">No messages yet</h3>
            <p className="text-eden-green-600 max-w-xs mx-auto">
              Start sending apples to make connections and begin conversations.
            </p>
          </div>
        )
      ) : (
        <div className="bg-white rounded-2xl p-6 leaf-shadow">
          <div className="flex items-start mb-6">
            <div className="w-10 h-10 rounded-full bg-eden-gold-100 flex items-center justify-center mr-3 mt-1">
              <i className="bi bi-key-fill text-eden-gold-500"></i>
            </div>
            <div>
              <h4 className="font-medium text-eden-green-800 mb-1">Unlock Who Likes You</h4>
              <p className="text-sm text-eden-green-700 mb-3">
                Upgrade to premium to see who's sent you apples without having to match first.
              </p>
              <button className="gold-button text-sm">
                Upgrade to Premium
              </button>
            </div>
          </div>
          
          <div className="bg-eden-green-50 rounded-xl p-4 text-center">
            <div className="flex justify-center mb-3">
              <i className="bi bi-apple text-apple-red-300 text-4xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-eden-green-800 mb-1">12 people like you</h3>
            <p className="text-eden-green-600 text-sm">
              Upgrade to see who they are and match instantly!
            </p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default Matches;
