# Adam & Eve Dating App 🍎

Modern, g<PERSON><PERSON><PERSON> ve kullanıc<PERSON> dostu bir dating uygulaması. React, TypeScript, Supabase ve Tailwind CSS ile geliştirilmiştir.

## ✨ Özellikler

### 🔐 Authentication & Security
- Supabase Auth ile güvenli kullanıcı kaydı ve girişi
- Email verification
- Password reset
- Row Level Security (RLS) ile veri güvenliği

### 👤 Profil Yönetimi
- Detaylı profil oluşturma
- Çoklu fotoğraf yükleme
- İl<PERSON> alanları ekleme
- Profil düzenleme

### 💕 Matching Sistemi
- Akıllı eşleşme algoritması
- Swipe tabanlı profil keşfi
- Gerçek zamanlı match bildirimleri
- Match geçmişi

### 💬 Mesajlaşma
- Gerçek zamanlı mesajlaşma
- Fotoğraf paylaşımı
- Mesaj okundu bilgisi
- Emoji desteği

### 📱 Mobile-First Design
- Responsive tasarım
- PWA (Progressive Web App) desteği
- Offline çalışma
- Native app benzeri deneyim

## 🛠️ Teknoloji Stack

### Frontend
- **React 18** - Modern UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animations
- **React Router** - Navigation
- **Vite** - Build tool

### Backend
- **Supabase** - Backend-as-a-Service
  - PostgreSQL database
  - Real-time subscriptions
  - Authentication
  - File storage
  - Row Level Security

### Mobile
- **Capacitor** - Native app wrapper
- **PWA** - Progressive Web App

## 🚀 Kurulum

### Ön Gereksinimler
- Node.js 18+ (Capacitor için 20+)
- npm veya yarn
- Git

### 1. Repository'yi klonlayın
```bash
git clone https://github.com/yourusername/adam-eve-dating-app.git
cd adam-eve-dating-app
```

### 2. Dependencies'i yükleyin
```bash
npm install
```

### 3. Environment variables'ı ayarlayın
```bash
cp .env.example .env
```

`.env` dosyasını düzenleyin:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Supabase database'ini kurun
`scripts/setup-database.md` dosyasındaki adımları takip edin.

### 5. Development server'ı başlatın
```bash
npm run dev
```

Uygulama `http://localhost:5173` adresinde çalışacaktır.

## 📁 Proje Yapısı

```
src/
├── components/          # Reusable UI components
│   ├── Button.tsx
│   ├── ProfileCard.tsx
│   ├── MatchAnimation.tsx
│   └── ...
├── pages/              # Page components
│   ├── Home.tsx
│   ├── Profile.tsx
│   ├── Matches.tsx
│   └── ...
├── services/           # API services
│   ├── authService.ts
│   ├── profileService.ts
│   ├── matchService.ts
│   └── messageService.ts
├── context/            # React contexts
│   └── AuthContext.tsx
├── lib/                # Utilities and configurations
│   └── supabase.ts
└── types/              # TypeScript type definitions
```

## 🗄️ Database Schema

### Ana Tablolar
- `profiles` - Kullanıcı profilleri
- `profile_images` - Profil fotoğrafları
- `user_interests` - Kullanıcı ilgi alanları
- `swipes` - Beğeni/beğenmeme kayıtları
- `matches` - Eşleşmeler
- `messages` - Mesajlar

Detaylı schema için `supabase/schema.sql` dosyasına bakın.

## 🔧 Available Scripts

```bash
# Development
npm run dev              # Development server başlat
npm run build           # Production build
npm run preview         # Production build'i preview et

# Type checking
npm run type-check      # TypeScript type check

# Mobile (Node.js 20+ gerekli)
npm run build:android   # Android app build
npm run build:ios       # iOS app build
```

## 🚀 Deployment

### PWA Deployment
1. Production build oluşturun: `npm run build`
2. `dist/` klasörünü hosting servisinize upload edin
3. HTTPS zorunludur (PWA için)

### Mobile App Deployment
Detaylı deployment rehberi için `docs/deployment-guide.md` dosyasına bakın.

### Önerilen Hosting Servisleri
- **Vercel** - Otomatik deployment
- **Netlify** - PWA optimized
- **Firebase Hosting** - Google ecosystem
- **AWS Amplify** - Scalable hosting

## 🔒 Güvenlik

### Implemented Security Measures
- Row Level Security (RLS) policies
- JWT token authentication
- Input validation
- XSS protection
- CSRF protection
- Rate limiting (Supabase level)

### Best Practices
- Environment variables kullanımı
- API key'lerin güvenli saklanması
- User data encryption
- Regular security updates

## 🧪 Testing

### Test Scenarios
- Authentication flow
- Profile creation/editing
- Swipe functionality
- Matching system
- Real-time messaging
- Offline functionality

### Test Commands
```bash
npm run test            # Unit tests
npm run test:e2e        # End-to-end tests
npm run test:coverage   # Coverage report
```

## 📱 PWA Features

- ✅ Offline support
- ✅ Install prompt
- ✅ Push notifications (ready)
- ✅ Background sync
- ✅ Responsive design
- ✅ Fast loading

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

### Development Guidelines
- TypeScript kullanın
- ESLint rules'a uyun
- Component'ları test edin
- Responsive design uygulayın
- Accessibility standartlarına uyun

## 📄 License

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 🆘 Support

### Documentation
- [Setup Guide](scripts/setup-database.md)
- [Deployment Guide](docs/deployment-guide.md)
- [API Documentation](docs/api.md)

### Community
- GitHub Issues
- Discord Server
- Stack Overflow

### Professional Support
Ticari destek için iletişime geçin.

## 🙏 Acknowledgments

- [Supabase](https://supabase.com) - Amazing backend platform
- [React](https://reactjs.org) - UI framework
- [Tailwind CSS](https://tailwindcss.com) - CSS framework
- [Framer Motion](https://framer.com/motion) - Animation library
- [Unsplash](https://unsplash.com) - Demo images

---

Made with ❤️ for meaningful connections
