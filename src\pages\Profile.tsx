import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const Profile: React.FC = () => {
  const { user, logout } = useAuth();
  
  if (!user) return null;
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">My Profile</h1>
        
        <Link to="/settings">
          <button className="w-10 h-10 rounded-full bg-white flex items-center justify-center leaf-shadow">
            <i className="bi bi-gear text-eden-green-600"></i>
          </button>
        </Link>
      </header>
      
      {/* Profile header */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white rounded-2xl overflow-hidden leaf-shadow mb-6"
      >
        <div className="relative h-40 bg-eden-green-200">
          <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2">
            <div className="relative">
              <div className="w-32 h-32 rounded-full border-4 border-white overflow-hidden">
                <img 
                  src={user.profilePicture || "https://placehold.co/200x200/65ae65/FFFFFF?text=Profile"} 
                  alt="Profile" 
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                />
              </div>
              
              <div className="absolute bottom-0 right-0 bg-white rounded-full p-2 leaf-shadow">
                <i className={`bi ${user.userType === 'adam' ? 'bi-gender-male' : user.userType === 'eve' ? 'bi-gender-female' : 'bi-asterisk'} text-lg ${user.userType === 'adam' ? 'text-eden-earth-500' : user.userType === 'eve' ? 'text-apple-red-500' : 'text-eden-gold-500'}`}></i>
              </div>
            </div>
          </div>
        </div>
        
        <div className="pt-20 pb-6 px-6 text-center">
          <h2 className="text-2xl font-bold text-eden-green-800 mb-1">{user.name}</h2>
          <p className="text-eden-green-600 mb-4">{user.email}</p>
          
          <div className="flex justify-center gap-3 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-eden-green-700">24</div>
              <div className="text-sm text-eden-green-600">Matches</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-eden-green-700">156</div>
              <div className="text-sm text-eden-green-600">Apples Sent</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-eden-green-700">142</div>
              <div className="text-sm text-eden-green-600">Apples Received</div>
            </div>
          </div>
          
          <Link to="/edit-profile">
            <Button variant="green" icon="bi-pencil">
              Edit Profile
            </Button>
          </Link>
        </div>
      </motion.div>
      
      {/* Membership section */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-eden-green-800">Membership</h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${user.isPremium ? 'bg-eden-gold-100 text-eden-gold-700' : 'bg-eden-green-100 text-eden-green-700'}`}>
            {user.isPremium ? 'Premium' : 'Free'}
          </div>
        </div>
        
        {!user.isPremium && (
          <div className="bg-eden-gold-50 rounded-xl p-4 mb-4">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-eden-gold-100 flex items-center justify-center mr-3 mt-1">
                <i className="bi bi-key-fill text-eden-gold-500"></i>
              </div>
              <div>
                <h4 className="font-medium text-eden-green-800 mb-1">Unlock the Secret Garden</h4>
                <p className="text-sm text-eden-green-700 mb-3">
                  Get unlimited apples, see who likes you, and access exclusive features.
                </p>
                <Button variant="gold" size="sm">
                  Upgrade to Premium
                </Button>
              </div>
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-apple text-apple-red-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Daily Apples</span>
            </div>
            <span className="font-medium text-eden-green-700">
              {user.isPremium ? 'Unlimited' : '10/20'}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-eye text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Who Likes You</span>
            </div>
            <span className={user.isPremium ? 'text-eden-green-700' : 'text-eden-green-400'}>
              {user.isPremium ? 'Unlocked' : 'Locked'}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-arrow-repeat text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Undo Last Swipe</span>
            </div>
            <span className={user.isPremium ? 'text-eden-green-700' : 'text-eden-green-400'}>
              {user.isPremium ? 'Unlocked' : 'Locked'}
            </span>
          </div>
        </div>
      </motion.div>
      
      {/* Account actions */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <h3 className="text-lg font-semibold text-eden-green-800 mb-4">Account</h3>
        
        <div className="space-y-4">
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-shield-check text-eden-green-500 mr-3 text-xl"></i>
              <span>Privacy Settings</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-bell text-eden-green-500 mr-3 text-xl"></i>
              <span>Notifications</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-question-circle text-eden-green-500 mr-3 text-xl"></i>
              <span>Help & Support</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button 
            onClick={logout}
            className="w-full flex justify-between items-center py-2 text-apple-red-500 hover:text-apple-red-600"
          >
            <div className="flex items-center">
              <i className="bi bi-box-arrow-right mr-3 text-xl"></i>
              <span>Sign Out</span>
            </div>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default Profile;
