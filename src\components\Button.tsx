import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'apple' | 'gold' | 'green' | 'earth' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  className?: string;
  icon?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  type = 'button',
  variant = 'apple',
  size = 'md',
  fullWidth = false,
  disabled = false,
  className = '',
  icon
}) => {
  const baseClasses = 'rounded-full font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center';
  
  const variantClasses = {
    apple: 'bg-apple-red-500 hover:bg-apple-red-600 text-white apple-shadow',
    gold: 'bg-eden-gold-500 hover:bg-eden-gold-600 text-eden-green-900 gold-shadow',
    green: 'bg-eden-green-500 hover:bg-eden-green-600 text-white leaf-shadow',
    earth: 'bg-eden-earth-500 hover:bg-eden-earth-600 text-white earth-shadow',
    outline: 'bg-transparent border-2 border-eden-green-500 text-eden-green-500 hover:bg-eden-green-50'
  };
  
  const sizeClasses = {
    sm: 'py-2 px-4 text-sm',
    md: 'py-3 px-6 text-base',
    lg: 'py-4 px-8 text-lg'
  };
  
  const widthClass = fullWidth ? 'w-full' : '';
  const disabledClass = disabled ? 'opacity-50 cursor-not-allowed hover:scale-100' : '';
  
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${widthClass}
        ${disabledClass}
        ${className}
      `}
    >
      {icon && <i className={`bi ${icon} ${children ? 'mr-2' : ''}`}></i>}
      {children}
    </button>
  );
};

export default Button;
