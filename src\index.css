@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --eden-green-500: #488f48;
  --apple-red-500: #f83b3b;
  --eden-gold-500: #efb506;
  --eden-earth-500: #b58455;
}

body {
  @apply font-garden bg-eden-green-50 text-eden-green-900 min-h-screen;
  -webkit-tap-highlight-color: transparent;
}

h1, h2, h3, h4, h5, h6 {
  @apply font-eden;
}

.garden-gradient {
  background: linear-gradient(135deg, #f0f7f0 0%, #dcefdc 100%);
}

.apple-gradient {
  background: linear-gradient(135deg, #f83b3b 0%, #c11414 100%);
}

.gold-gradient {
  background: linear-gradient(135deg, #ffdf41 0%, #efb506 100%);
}

.earth-gradient {
  background: linear-gradient(135deg, #d2b894 0%, #b58455 100%);
}

.leaf-shadow {
  box-shadow: 0 4px 20px rgba(72, 143, 72, 0.15);
}

.apple-shadow {
  box-shadow: 0 4px 20px rgba(248, 59, 59, 0.2);
}

.gold-shadow {
  box-shadow: 0 4px 20px rgba(239, 181, 6, 0.2);
}

.earth-shadow {
  box-shadow: 0 4px 20px rgba(181, 132, 85, 0.15);
}

.garden-card {
  @apply bg-white rounded-2xl p-5 leaf-shadow;
}

.apple-button {
  @apply bg-apple-red-500 hover:bg-apple-red-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 apple-shadow;
}

.gold-button {
  @apply bg-eden-gold-500 hover:bg-eden-gold-600 text-eden-green-900 font-medium py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 gold-shadow;
}

.green-button {
  @apply bg-eden-green-500 hover:bg-eden-green-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 leaf-shadow;
}

.earth-button {
  @apply bg-eden-earth-500 hover:bg-eden-earth-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 earth-shadow;
}

.profile-card {
  @apply relative bg-white rounded-3xl overflow-hidden leaf-shadow;
  height: calc(100vh - 180px);
  max-height: 700px;
}

.swipe-buttons {
  @apply flex justify-center gap-4 mt-4;
}

.swipe-button {
  @apply flex items-center justify-center w-16 h-16 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 active:scale-95;
}

.match-animation {
  @apply fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70;
}

.vine-border {
  @apply relative;
}

.vine-border::before {
  content: "";
  @apply absolute top-0 left-0 w-full h-full border-2 border-eden-green-300 rounded-3xl;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100' fill='none'%3E%3Cpath d='M0,0 C30,10 70,10 100,0 C90,30 90,70 100,100 C70,90 30,90 0,100 C10,70 10,30 0,0 Z' fill='%2365ae65' fill-opacity='0.1'/%3E%3C/svg%3E");
  background-size: 50px;
  background-repeat: repeat;
  z-index: -1;
}

.leaf-pattern {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60' fill='none'%3E%3Cpath d='M30,10 Q40,0 50,10 T70,10 Q60,20 70,30 T70,50 Q60,40 50,50 T30,50 Q40,40 30,30 T10,30 Q20,20 10,10 T30,10' fill='%2365ae65' fill-opacity='0.05'/%3E%3C/svg%3E");
}

.snake-pattern {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100' fill='none'%3E%3Cpath d='M0,50 Q25,0 50,50 T100,50' stroke='%23efb506' stroke-opacity='0.1' stroke-width='5' fill='none'/%3E%3C/svg%3E");
  background-size: 100px;
  background-repeat: repeat-x;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-eden-green-50;
}

::-webkit-scrollbar-thumb {
  @apply bg-eden-green-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-eden-green-400;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes sway {
  0%, 100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-sway {
  animation: sway 4s ease-in-out infinite;
}

.apple-fall {
  animation: fall 1s ease-in forwards;
}

@keyframes fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.pulse-heart {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
