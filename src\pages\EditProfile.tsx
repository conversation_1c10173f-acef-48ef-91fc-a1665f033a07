import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Input from '../components/Input';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const EditProfile: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [name, setName] = useState(user?.name || '');
  const [bio, setBio] = useState('I love exploring nature, trying new foods, and having deep conversations. Looking for someone who shares my passion for adventure and growth.');
  const [location, setLocation] = useState('New York, NY');
  const [interests, setInterests] = useState(['Nature', 'Food', 'Travel', 'Art', 'Music']);
  const [newInterest, setNewInterest] = useState('');
  
  const handleSave = () => {
    // In a real app, you would save the profile changes to the backend
    // For now, just navigate back to the profile page
    navigate('/profile');
  };
  
  const addInterest = () => {
    if (newInterest.trim() && !interests.includes(newInterest.trim())) {
      setInterests([...interests, newInterest.trim()]);
      setNewInterest('');
    }
  };
  
  const removeInterest = (interest: string) => {
    setInterests(interests.filter(i => i !== interest));
  };
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="flex justify-between items-center mb-6">
        <Link to="/profile" className="inline-flex items-center text-eden-green-700 hover:text-eden-green-800">
          <i className="bi bi-arrow-left text-xl mr-2"></i>
          <span>Back</span>
        </Link>
        
        <h1 className="text-xl font-bold text-eden-green-800 font-eden">Edit Profile</h1>
        
        <div className="w-8"></div> {/* Spacer for alignment */}
      </header>
      
      {/* Main content */}
      <main>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
        >
          {/* Profile picture */}
          <div className="flex flex-col items-center mb-6">
            <div className="relative mb-4">
              <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-eden-green-200">
                <img 
                  src={user?.profilePicture || "https://placehold.co/200x200/65ae65/FFFFFF?text=Profile"} 
                  alt="Profile" 
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                />
              </div>
              
              <button className="absolute bottom-0 right-0 bg-eden-green-500 text-white rounded-full p-2 leaf-shadow">
                <i className="bi bi-pencil text-sm"></i>
              </button>
            </div>
            
            <p className="text-eden-green-600 text-sm">
              Tap to change your profile picture
            </p>
          </div>
          
          {/* Basic info */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-eden-green-800 mb-4">Basic Information</h3>
            
            <Input
              label="Name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              icon="bi-person"
            />
            
            <div className="mb-4">
              <label className="block text-eden-green-700 font-medium mb-2">
                Bio
              </label>
              <textarea
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                className="w-full py-3 px-4 bg-white border-2 border-eden-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-eden-green-300 focus:border-eden-green-400 transition-all duration-200 min-h-[120px]"
                placeholder="Tell others about yourself..."
              />
              <p className="mt-1 text-eden-green-500 text-sm text-right">
                {bio.length}/300
              </p>
            </div>
            
            <Input
              label="Location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              icon="bi-geo-alt"
            />
          </div>
          
          {/* Interests */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-eden-green-800 mb-4">Interests</h3>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {interests.map((interest, index) => (
                <div 
                  key={index}
                  className="px-3 py-1 bg-eden-green-100 text-eden-green-700 rounded-full flex items-center"
                >
                  <span>{interest}</span>
                  <button 
                    onClick={() => removeInterest(interest)}
                    className="ml-2 text-eden-green-500 hover:text-eden-green-700"
                  >
                    <i className="bi bi-x"></i>
                  </button>
                </div>
              ))}
            </div>
            
            <div className="flex">
              <Input
                placeholder="Add a new interest"
                value={newInterest}
                onChange={(e) => setNewInterest(e.target.value)}
                className="flex-1 mb-0"
              />
              <button 
                onClick={addInterest}
                className="ml-2 bg-eden-green-500 text-white rounded-xl px-4 flex items-center justify-center"
                disabled={!newInterest.trim()}
              >
                <i className="bi bi-plus-lg"></i>
              </button>
            </div>
          </div>
          
          {/* Preferences */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-eden-green-800 mb-4">Preferences</h3>
            
            <div className="mb-4">
              <label className="block text-eden-green-700 font-medium mb-2">
                I'm interested in
              </label>
              
              <div className="flex gap-2">
                <div className="flex-1 p-3 rounded-xl border-2 border-eden-earth-500 bg-eden-earth-50 text-center">
                  <i className="bi bi-gender-male text-eden-earth-500 text-xl mb-1"></i>
                  <div className="text-eden-green-800 font-medium">Adam</div>
                </div>
                
                <div className="flex-1 p-3 rounded-xl border-2 border-eden-green-200 hover:border-apple-red-300 text-center">
                  <i className="bi bi-gender-female text-eden-green-500 text-xl mb-1"></i>
                  <div className="text-eden-green-800 font-medium">Eve</div>
                </div>
                
                <div className="flex-1 p-3 rounded-xl border-2 border-eden-green-200 hover:border-eden-gold-300 text-center">
                  <i className="bi bi-asterisk text-eden-green-500 text-xl mb-1"></i>
                  <div className="text-eden-green-800 font-medium">Both</div>
                </div>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-eden-green-700 font-medium mb-2">
                Age Range
              </label>
              
              <div className="flex items-center justify-between">
                <span className="text-eden-green-800 font-medium">21</span>
                <div className="flex-1 mx-4 h-2 bg-eden-green-100 rounded-full relative">
                  <div className="absolute left-[20%] right-[20%] h-full bg-eden-green-500 rounded-full"></div>
                  <div className="absolute left-[20%] top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white border-2 border-eden-green-500 rounded-full"></div>
                  <div className="absolute right-[20%] top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white border-2 border-eden-green-500 rounded-full"></div>
                </div>
                <span className="text-eden-green-800 font-medium">45</span>
              </div>
            </div>
            
            <div>
              <label className="block text-eden-green-700 font-medium mb-2">
                Distance
              </label>
              
              <div className="flex items-center justify-between">
                <span className="text-eden-green-800 font-medium">5 miles</span>
                <div className="flex-1 mx-4 h-2 bg-eden-green-100 rounded-full relative">
                  <div className="absolute left-0 right-[40%] h-full bg-eden-green-500 rounded-full"></div>
                  <div className="absolute right-[40%] top-1/2 transform -translate-y-1/2 w-5 h-5 bg-white border-2 border-eden-green-500 rounded-full"></div>
                </div>
                <span className="text-eden-green-800 font-medium">50 miles</span>
              </div>
            </div>
          </div>
          
          {/* Save button */}
          <Button
            variant="apple"
            fullWidth
            onClick={handleSave}
            icon="bi-check-lg"
          >
            Save Changes
          </Button>
        </motion.div>
      </main>
    </div>
  );
};

export default EditProfile;
