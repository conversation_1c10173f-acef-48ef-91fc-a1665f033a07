import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const Settings: React.FC = () => {
  const { user, logout } = useAuth();
  const [notifications, setNotifications] = useState({
    matches: true,
    messages: true,
    likes: true,
    events: true,
    marketing: false
  });
  
  const toggleNotification = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="flex items-center mb-6">
        <Link to="/profile" className="mr-4">
          <i className="bi bi-arrow-left text-eden-green-700 text-xl"></i>
        </Link>
        <h1 className="text-2xl font-bold text-eden-green-800 font-eden">Settings</h1>
      </header>
      
      {/* Account settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Account</h2>
        
        <div className="space-y-4">
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-person text-eden-green-500 mr-3 text-xl"></i>
              <span>Personal Information</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-lock text-eden-green-500 mr-3 text-xl"></i>
              <span>Password</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-envelope text-eden-green-500 mr-3 text-xl"></i>
              <span>Email</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-phone text-eden-green-500 mr-3 text-xl"></i>
              <span>Phone Number</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
        </div>
      </motion.div>
      
      {/* Notifications */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Notifications</h2>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-heart text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">New Matches</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer" 
                checked={notifications.matches}
                onChange={() => toggleNotification('matches')}
              />
              <div className="w-11 h-6 bg-eden-green-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eden-green-500"></div>
            </label>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-chat text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Messages</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer" 
                checked={notifications.messages}
                onChange={() => toggleNotification('messages')}
              />
              <div className="w-11 h-6 bg-eden-green-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eden-green-500"></div>
            </label>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-apple text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Likes</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer" 
                checked={notifications.likes}
                onChange={() => toggleNotification('likes')}
              />
              <div className="w-11 h-6 bg-eden-green-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eden-green-500"></div>
            </label>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-calendar-event text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Events</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer" 
                checked={notifications.events}
                onChange={() => toggleNotification('events')}
              />
              <div className="w-11 h-6 bg-eden-green-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eden-green-500"></div>
            </label>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <i className="bi bi-megaphone text-eden-green-500 mr-3 text-xl"></i>
              <span className="text-eden-green-800">Marketing</span>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer" 
                checked={notifications.marketing}
                onChange={() => toggleNotification('marketing')}
              />
              <div className="w-11 h-6 bg-eden-green-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eden-green-500"></div>
            </label>
          </div>
        </div>
      </motion.div>
      
      {/* Privacy */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Privacy & Safety</h2>
        
        <div className="space-y-4">
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-shield-check text-eden-green-500 mr-3 text-xl"></i>
              <span>Privacy Settings</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-eye-slash text-eden-green-500 mr-3 text-xl"></i>
              <span>Blocked Users</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-flag text-eden-green-500 mr-3 text-xl"></i>
              <span>Report a Concern</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
        </div>
      </motion.div>
      
      {/* Support */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-2xl p-6 leaf-shadow mb-6"
      >
        <h2 className="text-lg font-semibold text-eden-green-800 mb-4">Support</h2>
        
        <div className="space-y-4">
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-question-circle text-eden-green-500 mr-3 text-xl"></i>
              <span>Help Center</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-chat-dots text-eden-green-500 mr-3 text-xl"></i>
              <span>Contact Support</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-file-earmark-text text-eden-green-500 mr-3 text-xl"></i>
              <span>Terms of Service</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
          
          <button className="w-full flex justify-between items-center py-2 text-eden-green-800 hover:text-eden-green-600">
            <div className="flex items-center">
              <i className="bi bi-file-earmark-lock text-eden-green-500 mr-3 text-xl"></i>
              <span>Privacy Policy</span>
            </div>
            <i className="bi bi-chevron-right"></i>
          </button>
        </div>
      </motion.div>
      
      {/* Account actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <Button
          variant="outline"
          fullWidth
          onClick={() => {}}
          icon="bi-pause-circle"
        >
          Pause My Account
        </Button>
        
        <Button
          variant="apple"
          fullWidth
          onClick={logout}
          icon="bi-box-arrow-right"
        >
          Sign Out
        </Button>
        
        <button className="w-full py-3 text-apple-red-500 font-medium">
          Delete Account
        </button>
      </motion.div>
    </div>
  );
};

export default Settings;
