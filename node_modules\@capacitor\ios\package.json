{"name": "@capacitor/ios", "version": "7.2.0", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["Capacitor/Capacitor/", "CapacitorCordova/CapacitorCordova/", "Capacitor.podspec", "CapacitorCordova.podspec", "scripts/pods_helpers.rb"], "scripts": {"verify": "npm run xc:build:Capacitor && npm run xc:build:CapacitorCordova", "xc:build:Capacitor": "cd Capacitor && xcodebuild clean test -workspace Capacitor.xcworkspace -scheme Capacitor -destination 'platform=iOS Simulator,name=iPhone 16,OS=18.0' && cd ..", "xc:build:CapacitorCordova": "cd CapacitorCordova && xcodebuild && cd ..", "xc:build:xcframework": "scripts/build.sh xcframework"}, "peerDependencies": {"@capacitor/core": "^7.2.0"}, "publishConfig": {"access": "public"}}