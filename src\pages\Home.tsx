import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProfileCard from '../components/ProfileCard';
import MatchAnimation from '../components/MatchAnimation';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

// Mock data
const mockProfiles = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    location: 'New York, NY',
    bio: 'Passionate about art, nature, and meaningful conversations. Looking for someone who appreciates the simple things in life and isn\'t afraid to be vulnerable.',
    images: [
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?auto=format&fit=crop&w=800&q=80'
    ],
    interests: ['Art', 'Hiking', 'Philosophy', 'Cooking', 'Travel'],
    userType: 'eve' as const
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 31,
    location: 'Los Angeles, CA',
    bio: 'Adventure seeker and coffee enthusiast. I believe in living life to the fullest and finding joy in everyday moments. Let\'s explore the world together!',
    images: [
      'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1488161628813-04466f872be2?auto=format&fit=crop&w=800&q=80'
    ],
    interests: ['Photography', 'Coffee', 'Surfing', 'Meditation', 'Music'],
    userType: 'adam' as const
  },
  {
    id: '3',
    name: 'Olivia',
    age: 26,
    location: 'Chicago, IL',
    bio: 'Book lover, yoga instructor, and plant mom. Seeking someone who values growth, authenticity, and deep connections. Bonus points if you love dogs!',
    images: [
      'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=800&q=80'
    ],
    interests: ['Yoga', 'Reading', 'Plants', 'Dogs', 'Mindfulness'],
    userType: 'eve' as const
  },
  {
    id: '4',
    name: 'Ethan',
    age: 29,
    location: 'Seattle, WA',
    bio: 'Tech enthusiast by day, musician by night. I enjoy hiking in the mountains, trying new restaurants, and having thought-provoking conversations.',
    images: [
      'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=800&q=80'
    ],
    interests: ['Music', 'Technology', 'Hiking', 'Food', 'Philosophy'],
    userType: 'adam' as const
  },
  {
    id: '5',
    name: 'Luna',
    age: 27,
    location: 'Austin, TX',
    bio: 'Free spirit with a passion for sustainability and creative expression. Looking for someone who appreciates authenticity and isn\'t afraid to dance in the rain.',
    images: [
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=800&q=80'
    ],
    interests: ['Sustainability', 'Art', 'Dancing', 'Nature', 'Spirituality'],
    userType: 'garden-spirit' as const
  }
];

const Home: React.FC = () => {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState(mockProfiles);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showMatch, setShowMatch] = useState(false);
  const [matchedUser, setMatchedUser] = useState<{ name: string; image: string } | null>(null);
  const [noMoreProfiles, setNoMoreProfiles] = useState(false);
  
  // Filter profiles based on user preferences (simplified)
  useEffect(() => {
    if (user) {
      // In a real app, you would filter based on user preferences
      // For now, just filter out profiles that don't match the user's type preference
      const filteredProfiles = mockProfiles.filter(profile => {
        if (user.userType === 'adam') return profile.userType !== 'adam';
        if (user.userType === 'eve') return profile.userType !== 'eve';
        return true; // garden-spirit can see all
      });
      
      setProfiles(filteredProfiles);
      setCurrentIndex(0);
      setNoMoreProfiles(filteredProfiles.length === 0);
    }
  }, [user]);
  
  const handleSwipe = (direction: 'left' | 'right') => {
    // If right swipe (like), randomly determine if it's a match
    if (direction === 'right' && Math.random() > 0.6) {
      const currentProfile = profiles[currentIndex];
      setMatchedUser({
        name: currentProfile.name,
        image: currentProfile.images[0]
      });
      setShowMatch(true);
    }
    
    // Move to next profile
    if (currentIndex < profiles.length - 1) {
      setCurrentIndex(prevIndex => prevIndex + 1);
    } else {
      setNoMoreProfiles(true);
    }
  };
  
  const resetProfiles = () => {
    // In a real app, you would fetch new profiles
    // For demo, just reset to the beginning
    setCurrentIndex(0);
    setNoMoreProfiles(false);
  };
  
  return (
    <div className="min-h-screen bg-eden-green-50 pt-6 px-4 pb-20">
      {/* Header */}
      <header className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <i className="bi bi-apple text-apple-red-500 text-3xl mr-2"></i>
          <h1 className="text-xl font-bold text-eden-green-800 font-eden">Adam & Eve</h1>
        </div>
        
        <div className="flex items-center">
          <button className="w-10 h-10 rounded-full bg-white flex items-center justify-center leaf-shadow mr-2">
            <i className="bi bi-sliders text-eden-green-600"></i>
          </button>
          
          <button className="w-10 h-10 rounded-full bg-white flex items-center justify-center leaf-shadow">
            <i className="bi bi-bell text-eden-green-600"></i>
          </button>
        </div>
      </header>
      
      {/* Main content */}
      <main>
        {noMoreProfiles ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center h-[calc(100vh-200px)]"
          >
            <div className="text-center mb-8">
              <div className="flex justify-center mb-6">
                <i className="bi bi-tree text-eden-green-400 text-6xl"></i>
              </div>
              <h2 className="text-2xl font-bold text-eden-green-800 mb-3 font-eden">
                No More Profiles
              </h2>
              <p className="text-eden-green-600 max-w-xs mx-auto">
                You've explored all available profiles in the Garden. Check back later for new connections.
              </p>
            </div>
            
            <Button
              variant="green"
              onClick={resetProfiles}
              icon="bi-arrow-repeat"
            >
              Refresh Garden
            </Button>
          </motion.div>
        ) : (
          <>
            <AnimatePresence>
              {profiles.length > 0 && (
                <div className="flex justify-center mb-6">
                  <ProfileCard
                    key={profiles[currentIndex].id}
                    profile={profiles[currentIndex]}
                    onSwipe={handleSwipe}
                  />
                </div>
              )}
            </AnimatePresence>
            
            <div className="swipe-buttons">
              <button 
                onClick={() => handleSwipe('left')}
                className="swipe-button bg-white text-eden-green-600"
              >
                <i className="bi bi-x-lg text-2xl"></i>
              </button>
              
              <button 
                className="swipe-button bg-eden-gold-500 text-eden-green-900"
              >
                <i className="bi bi-star-fill text-xl"></i>
              </button>
              
              <button 
                onClick={() => handleSwipe('right')}
                className="swipe-button bg-apple-red-500 text-white"
              >
                <i className="bi bi-apple text-2xl"></i>
              </button>
            </div>
          </>
        )}
      </main>
      
      {/* Match animation */}
      <AnimatePresence>
        {showMatch && matchedUser && (
          <MatchAnimation
            matchedUser={matchedUser}
            onClose={() => setShowMatch(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Home;
