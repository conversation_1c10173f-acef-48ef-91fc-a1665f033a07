import React, { useState, useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';

// Pages
import SplashScreen from './pages/SplashScreen';
import Welcome from './pages/Welcome';
import Login from './pages/Login';
import Register from './pages/Register';
import Home from './pages/Home';
import Profile from './pages/Profile';
import EditProfile from './pages/EditProfile';
import Matches from './pages/Matches';
import Chat from './pages/Chat';
import GardenParty from './pages/GardenParty';
import Settings from './pages/Settings';
import WisdomTree from './pages/WisdomTree';
import SecretGarden from './pages/SecretGarden';

// Components
import BottomNavigation from './components/BottomNavigation';
import ProtectedRoute from './components/ProtectedRoute';

// Context
import { AuthProvider } from './context/AuthContext';

const App: React.FC = () => {
  const location = useLocation();
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const showNavigation = !['/', '/login', '/register'].includes(location.pathname) && !showSplash;

  if (showSplash) {
    return <SplashScreen />;
  }

  return (
    <AuthProvider>
      <div className="app-container relative min-h-screen pb-16">
        <AnimatePresence mode="wait">
          <Routes location={location} key={location.pathname}>
            <Route path="/" element={<Welcome />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            
            <Route path="/home" element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            } />
            
            <Route path="/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />
            
            <Route path="/edit-profile" element={
              <ProtectedRoute>
                <EditProfile />
              </ProtectedRoute>
            } />
            
            <Route path="/matches" element={
              <ProtectedRoute>
                <Matches />
              </ProtectedRoute>
            } />
            
            <Route path="/chat/:id" element={
              <ProtectedRoute>
                <Chat />
              </ProtectedRoute>
            } />
            
            <Route path="/garden-party" element={
              <ProtectedRoute>
                <GardenParty />
              </ProtectedRoute>
            } />
            
            <Route path="/wisdom-tree" element={
              <ProtectedRoute>
                <WisdomTree />
              </ProtectedRoute>
            } />
            
            <Route path="/secret-garden" element={
              <ProtectedRoute>
                <SecretGarden />
              </ProtectedRoute>
            } />
            
            <Route path="/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />
          </Routes>
        </AnimatePresence>
        
        {showNavigation && <BottomNavigation />}
      </div>
    </AuthProvider>
  );
};

export default App;
