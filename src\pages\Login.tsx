import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Input from '../components/Input';
import Button from '../components/Button';
import { useAuth } from '../context/AuthContext';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { login } = useAuth();
  const navigate = useNavigate();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    
    try {
      await login(email, password);
      navigate('/home');
    } catch (err) {
      setError('Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      {/* Background with garden pattern */}
      <div className="fixed inset-0 leaf-pattern opacity-20 z-0"></div>
      
      {/* Content */}
      <div className="flex-1 flex flex-col relative z-10">
        {/* Back button */}
        <header className="pt-6 px-6">
          <Link to="/" className="inline-flex items-center text-eden-green-700 hover:text-eden-green-800">
            <i className="bi bi-arrow-left text-xl mr-2"></i>
            <span>Back</span>
          </Link>
        </header>
        
        {/* Main content */}
        <main className="flex-1 flex flex-col items-center justify-center px-6 py-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <i className="bi bi-apple text-apple-red-500 text-4xl"></i>
              </div>
              <h2 className="text-3xl font-bold text-eden-green-800 mb-2 font-eden">
                Welcome Back
              </h2>
              <p className="text-eden-green-700">
                Sign in to continue your journey in Eden
              </p>
            </div>
            
            <div className="bg-white rounded-2xl p-6 leaf-shadow">
              {error && (
                <div className="mb-4 p-3 bg-apple-red-50 border border-apple-red-200 rounded-lg text-apple-red-700 text-sm">
                  <div className="flex items-center">
                    <i className="bi bi-exclamation-circle mr-2"></i>
                    <span>{error}</span>
                  </div>
                </div>
              )}
              
              <form onSubmit={handleSubmit}>
                <Input
                  type="email"
                  label="Email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  icon="bi-envelope"
                />
                
                <Input
                  type="password"
                  label="Password"
                  placeholder="Your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  icon="bi-lock"
                />
                
                <div className="flex justify-end mb-6">
                  <button type="button" className="text-sm text-eden-green-600 hover:text-eden-green-700">
                    Forgot password?
                  </button>
                </div>
                
                <Button
                  type="submit"
                  variant="apple"
                  fullWidth
                  disabled={isLoading}
                  icon={isLoading ? "bi-arrow-repeat" : "bi-box-arrow-in-right"}
                >
                  {isLoading ? 'Signing In...' : 'Sign In'}
                </Button>
              </form>
            </div>
            
            <div className="text-center mt-6">
              <p className="text-eden-green-700">
                New to Eden?{' '}
                <Link to="/register" className="text-apple-red-500 font-medium hover:text-apple-red-600">
                  Create an account
                </Link>
              </p>
            </div>
          </motion.div>
        </main>
        
        {/* Footer */}
        <footer className="py-6 text-center text-eden-green-600 text-sm">
          <p>Designed by WebSparks AI</p>
        </footer>
      </div>
    </div>
  );
};

export default Login;
