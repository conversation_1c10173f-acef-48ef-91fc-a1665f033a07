import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from '../components/Button';

const Welcome: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Background with garden pattern */}
      <div className="fixed inset-0 leaf-pattern opacity-20 z-0"></div>
      
      {/* Content */}
      <div className="flex-1 flex flex-col relative z-10">
        {/* Header */}
        <header className="pt-6 px-6 flex justify-center">
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center">
              <i className="bi bi-apple text-apple-red-500 text-4xl mr-2"></i>
              <h1 className="text-2xl font-bold text-eden-green-800 font-eden"><PERSON> & <PERSON></h1>
            </div>
          </motion.div>
        </header>
        
        {/* Main content */}
        <main className="flex-1 flex flex-col items-center justify-center px-6 py-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center mb-10"
          >
            <h2 className="text-3xl font-bold text-eden-green-800 mb-3 font-eden">
              Welcome to the Garden
            </h2>
            <p className="text-eden-green-700 text-lg max-w-md mx-auto">
              Discover meaningful connections in our modern Eden. Your perfect match is just an apple away.
            </p>
          </motion.div>
          
          {/* Animated illustration */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="relative w-64 h-64 mb-12"
          >
            <motion.div
              animate={{ 
                y: [0, -15, 0],
                rotate: [0, 5, 0, -5, 0]
              }}
              transition={{ 
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
              }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <div className="relative">
                <i className="bi bi-apple text-apple-red-500 text-8xl"></i>
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    opacity: [0.8, 1, 0.8]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  className="absolute -top-3 right-0"
                >
                  <i className="bi bi-heart-fill text-eden-gold-500 text-2xl"></i>
                </motion.div>
              </div>
            </motion.div>
            
            <motion.div
              animate={{ 
                rotate: [0, -3, 0, 3, 0]
              }}
              transition={{ 
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="absolute bottom-0 left-0 right-0 flex justify-center"
            >
              <div className="w-48 h-12 bg-eden-green-300 rounded-full opacity-20 filter blur-md"></div>
            </motion.div>
          </motion.div>
          
          {/* Action buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="w-full max-w-xs space-y-4"
          >
            <Link to="/register" className="block">
              <Button variant="apple" fullWidth icon="bi-person-plus">
                Create Account
              </Button>
            </Link>
            
            <Link to="/login" className="block">
              <Button variant="outline" fullWidth icon="bi-box-arrow-in-right">
                Already in Eden? Sign In
              </Button>
            </Link>
          </motion.div>
        </main>
        
        {/* Footer */}
        <footer className="py-6 text-center text-eden-green-600 text-sm">
          <p>Designed by WebSparks AI</p>
        </footer>
      </div>
    </div>
  );
};

export default Welcome;
