/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'eden-green': {
          50: '#f0f7f0',
          100: '#dcefdc',
          200: '#bde0bd',
          300: '#92ca92',
          400: '#65ae65',
          500: '#488f48',
          600: '#377437',
          700: '#2e5c2e',
          800: '#274927',
          900: '#223d22',
        },
        'apple-red': {
          50: '#fff1f1',
          100: '#ffe1e1',
          200: '#ffc7c7',
          300: '#ffa0a0',
          400: '#ff6b6b',
          500: '#f83b3b',
          600: '#e51d1d',
          700: '#c11414',
          800: '#a01414',
          900: '#841818',
        },
        'eden-gold': {
          50: '#fefbe8',
          100: '#fff8c2',
          200: '#ffee86',
          300: '#ffdf41',
          400: '#ffcf10',
          500: '#efb506',
          600: '#cc8a03',
          700: '#a36206',
          800: '#874d0c',
          900: '#733f10',
        },
        'eden-earth': {
          50: '#f9f6f1',
          100: '#f0e9dd',
          200: '#e2d3bc',
          300: '#d2b894',
          400: '#c19c6e',
          500: '#b58455',
          600: '#a06c47',
          700: '#85563c',
          800: '#6e4835',
          900: '#5c3d2f',
        },
      },
      fontFamily: {
        'garden': ['Quicksand', 'sans-serif'],
        'eden': ['Playfair Display', 'serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'sway': 'sway 4s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        sway: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
      },
    },
  },
  plugins: [],
}
