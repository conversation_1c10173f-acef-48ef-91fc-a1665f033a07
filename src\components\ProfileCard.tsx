import React from 'react';
import { motion } from 'framer-motion';
import { useSwipeable } from 'react-swipeable';

interface ProfileCardProps {
  profile: {
    id: string;
    name: string;
    age: number;
    location: string;
    bio: string;
    images: string[];
    interests: string[];
    userType: 'adam' | 'eve' | 'garden-spirit';
  };
  onSwipe: (direction: 'left' | 'right') => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ profile, onSwipe }) => {
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);
  const [showDetails, setShowDetails] = React.useState(false);

  const handlers = useSwipeable({
    onSwipedLeft: () => onSwipe('left'),
    onSwipedRight: () => onSwipe('right'),
    preventDefaultTouchmoveEvent: true,
    trackMouse: true
  });

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentImageIndex < profile.images.length - 1) {
      setCurrentImageIndex(prev => prev + 1);
    }
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentImageIndex > 0) {
      setCurrentImageIndex(prev => prev - 1);
    }
  };

  const toggleDetails = () => {
    setShowDetails(prev => !prev);
  };

  return (
    <motion.div
      {...handlers}
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="profile-card"
    >
      <div className="relative h-full">
        {/* Profile Images */}
        <div className="relative h-full">
          <img
            src={profile.images[currentImageIndex] || 'https://placehold.co/600x800/65ae65/FFFFFF?text=No+Image'}
            alt={`${profile.name}'s profile`}
            className="w-full h-full object-cover"
            crossOrigin="anonymous"
          />
          
          {/* Image Navigation */}
          <div className="absolute top-0 left-0 right-0 p-4 flex justify-between">
            <div className="flex space-x-1">
              {profile.images.map((_, index) => (
                <div 
                  key={index}
                  className={`h-1 rounded-full ${index === currentImageIndex ? 'bg-white w-6' : 'bg-white/50 w-4'}`}
                ></div>
              ))}
            </div>
          </div>
          
          <div className="absolute top-1/2 left-0 right-0 flex justify-between px-2">
            <button 
              onClick={prevImage}
              className={`w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center ${currentImageIndex === 0 ? 'opacity-0' : 'opacity-100'}`}
            >
              <i className="bi bi-chevron-left text-white text-xl"></i>
            </button>
            
            <button 
              onClick={nextImage}
              className={`w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center ${currentImageIndex === profile.images.length - 1 ? 'opacity-0' : 'opacity-100'}`}
            >
              <i className="bi bi-chevron-right text-white text-xl"></i>
            </button>
          </div>
          
          {/* User Type Badge */}
          <div className="absolute top-4 right-4">
            <div className={`
              px-3 py-1 rounded-full text-sm font-medium
              ${profile.userType === 'adam' ? 'bg-eden-earth-500 text-white' : 
                profile.userType === 'eve' ? 'bg-apple-red-500 text-white' : 
                'bg-eden-gold-500 text-eden-green-900'}
            `}>
              {profile.userType === 'adam' ? 'Adam' : 
               profile.userType === 'eve' ? 'Eve' : 
               'Garden Spirit'}
            </div>
          </div>
        </div>
        
        {/* Profile Info */}
        <div 
          className={`
            absolute bottom-0 left-0 right-0 p-6
            bg-gradient-to-t from-black/80 to-transparent
            text-white transition-all duration-300
            ${showDetails ? 'h-2/3' : 'h-auto'}
          `}
          onClick={toggleDetails}
        >
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold">{profile.name}, {profile.age}</h2>
              <p className="text-white/80 flex items-center">
                <i className="bi bi-geo-alt mr-1"></i> {profile.location}
              </p>
            </div>
            
            <button 
              onClick={(e) => {
                e.stopPropagation();
                toggleDetails();
              }}
              className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center"
            >
              <i className={`bi ${showDetails ? 'bi-chevron-down' : 'bi-chevron-up'} text-white`}></i>
            </button>
          </div>
          
          {showDetails && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="mt-4 overflow-y-auto max-h-[calc(100%-4rem)]"
            >
              <h3 className="text-lg font-semibold mb-2">About</h3>
              <p className="text-white/90 mb-4">{profile.bio}</p>
              
              <h3 className="text-lg font-semibold mb-2">Interests</h3>
              <div className="flex flex-wrap gap-2 mb-4">
                {profile.interests.map((interest, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-white/20 rounded-full text-sm"
                  >
                    {interest}
                  </span>
                ))}
              </div>
              
              <div className="text-center mt-6 text-white/60 text-sm">
                <p>Tap outside this area to close</p>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ProfileCard;
